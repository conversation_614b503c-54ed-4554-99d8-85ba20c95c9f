OpenFOAM「3-D flow past a circular cylinder」算例的完整说明。

---

### 1 坐标系与基准

* 原点 **(0 m, 0 m, 0 m)** 设在入口平面左下角。
* **x 轴**—来流方向；**y 轴**—竖直向上；**z 轴**—跨距方向（指向读者）。

### 2 计算域尺寸

| 方向      | 尺寸     | 说明                                |
| ------- | ------ | --------------------------------- |
| y（高度）   | 0.41 m | 通道顶部到底部                           |
| z（宽度）   | 0.41 m | 左右壁面间距                            |
| x（流向长度） | 2.50 m | 入口 → 圆柱前缘 0.45 m；圆柱后缘 → 出口 1.95 m |

### 3 圆柱几何

| 参数       | 数值                                                                                |
| -------- | --------------------------------------------------------------------------------- |
| 直径 **D** | 0.10 m                                                                            |
| 轴线方向     | 与 **z** 轴平行，跨越整个通道厚度                                                              |
| 圆心坐标     | (0.45 m, 0.20 m, 0.205 m) <br>*(x = 0.45 m；底部到圆心 0.15 m→y = 0.15 m + D/2；z 方向居中)* |

### 4 物性与工况

* 不可压缩牛顿流体，**动粘度** ν = 1.0 × 10⁻³ m²/s。
* 入口施加抛物线速度，截面平均值 **U**；计算雷诺数 **Re = UD/ν**。

### 5 边界命名与条件（OpenFOAM 推荐写法）

| patch 名          | 位置                | 类型                                                                 | 速度 **U**                         | 压力 **p**                |
| ---------------- | ----------------- | ------------------------------------------------------------------ | -------------------------------- | ----------------------- |
| **inlet**        | x = 0             | patch                                                              | `fixedValue` 抛物线 <br>(均值 = U)    | `zeroGradient`          |
| **outlet**       | x = 2.5 m         | patch                                                              | `zeroGradient`                   | `fixedValue 0` *(参考静压)* |
| **cylinder**     | 圆柱壁               | wall                                                               | `fixedValue (0 0 0)` *(no-slip)* | `zeroGradient`          |
| **topWall**      | y = 0.41 m        | wall                                                               | `fixedValue (0 0 0)`             | `zeroGradient`          |
| **bottomWall**   | y = 0             | wall                                                               | `fixedValue (0 0 0)`             | `zeroGradient`          |
| **front / back** | z = 0, z = 0.41 m | 两种做法：<br>① `wall` → 与图一致 `U=V=W=0`<br>② `symmetryPlane` → 做 2-D 近似 | 同左                               | 同左                      |

> **提示**：文献示意在所有外壁标注 *U = V = W = 0*，若需完美复现请选择 **wall**。如想节省网格量并做准 2-D 研究，可保持仅 1–4 个单元厚度并将 front/back 设为 **symmetryPlane**。

### 6 网格构建思路

1. 在 **yz 截面**先用 *blockMesh* 生成粗矩形网格并打孔（圆柱）。
2. 按图示对边连点细化两次（≈ 6144 cells in 2-D），再沿 z 方向均匀挤出 4 层得到 3-D 网格。
3. 若需更高精度，可在 *snappyHexMesh* 中对圆柱近壁加局部 refinement 区。

datasets/OpenFOAM_cases/Cylinder_flow_3D路径下现在整个3D圆柱绕流的openfoam流程基本都跑通了，你的设置这些都正确，只不过圆柱的表面网格质量不太好，我现在手动用UG软件生成了新的圆柱stl文件datasets/OpenFOAM_cases/Cylinder_flow_3D/constant/triSurface/cylinder_comsol.stl， 请你以这个文件重新用网盘的openfoam生成一下网格，并且你要给我生成边界层棱柱体网格（至少10层），你需要给我说你在哪个文件的哪些地方制定了边界层高度，和stretch率等变量，我可以尝试自己再调整一下参数，然后生成好网格之后可以直接开始计算，你的其他配置都非常正确