/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2406                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

7
(
    inlet
    {
        type            patch;
        nFaces          220;
        startFace       89382;
    }
    outlet
    {
        type            patch;
        nFaces          220;
        startFace       89602;
    }
    topWall
    {
        type            wall;
        inGroups        1(wall);
        nFaces          630;
        startFace       89822;
    }
    bottomWall
    {
        type            wall;
        inGroups        1(wall);
        nFaces          630;
        startFace       90452;
    }
    front
    {
        type            wall;
        inGroups        1(wall);
        nFaces          1858;
        startFace       91082;
    }
    back
    {
        type            wall;
        inGroups        1(wall);
        nFaces          1858;
        startFace       92940;
    }
    cylinder
    {
        type            wall;
        inGroups        2(cylinderGroup wall);
        nFaces          2080;
        startFace       94798;
    }
)

// ************************************************************************* //
