#!/bin/bash

# 实时监控脚本
echo "启动实时监控 Cd 和 Cl 值..."
echo "时间步 | Cd | Cl"
echo "------------------------"

# 监控postProcessing/forceCoeffs/0/coefficient.dat文件
tail -f postProcessing/forceCoeffs/0/coefficient.dat | while read line; do
    # 跳过注释行
    if [[ $line =~ ^# ]]; then
        continue
    fi
    
    # 解析数据：时间 Cd Cl Cm
    if [[ $line =~ ^[0-9] ]]; then
        time=$(echo $line | awk '{print $1}')
        cd=$(echo $line | awk '{print $2}')
        cl=$(echo $line | awk '{print $3}')
        printf "%8.3f | %8.4f | %8.4f\n" $time $cd $cl
    fi
done
