/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2406                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volVectorField;
    location    "0";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (1.0 0 0);

boundaryField
{
    inlet
    {
        type            fixedValue;
        value           uniform (1.0 0 0);
    }

    outlet
    {
        type            zeroGradient;
    }

    cylinder
    {
        type            noSlip;
    }

    topWall
    {
        type            noSlip;
    }

    bottomWall
    {
        type            noSlip;
    }

    front
    {
        type            noSlip;
    }

    back
    {
        type            noSlip;
    }
}

// ************************************************************************* //
