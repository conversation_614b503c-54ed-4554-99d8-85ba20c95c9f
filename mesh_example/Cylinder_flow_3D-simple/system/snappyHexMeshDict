/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      snappyHexMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

// 网格质量控制参数
castellatedMesh true;
snap            true;
addLayers       false; // 禁用边界层生成

// STL几何文件定义
geometry
{
    cylinder_comsol_quad.stl
    {
        type triSurfaceMesh;
        name cylinder;
    }
}

// 网格细化参数
castellatedMeshControls
{
    // 最大局部细化级别
    maxLocalCells 10000;
    
    // 最大全局细化级别
    maxGlobalCells 30000;
    
    // 最小细化级别
    minRefinementCells 10;
    
    // 最大不一致级别
    maxLoadUnbalance 0.10;
    
    // 缓冲层数量
    nCellsBetweenLevels 3;
    
    // 表面特征细化
    features
    (
    );
    
    // 表面细化级别
    refinementSurfaces
    {
        cylinder
        {
            level (1 2);  // 恢复表面细化
            
            // 指定边界patch类型
            patchInfo
            {
                type wall;
                inGroups (cylinderGroup);
            }
        }
    }
    
    // 特定表面patch类型覆盖
    resolveFeatureAngle 30;
    
    // 区域细化（在圆柱附近）
    refinementRegions
    {
        cylinder
        {
            mode distance;
            levels ((0.05 2) (0.1 1));
        }
    }
    
    // 位置检测点 (确保在流体域内)
    locationInMesh (1.0 0.2 0.2);
    
    // 网格收敛性控制
    allowFreeStandingZoneFaces true;
}

// 网格贴合参数
snapControls
{
    // 贴合迭代次数
    nSmoothPatch 5;
    
    // 网格质量控制的容忍度
    tolerance 2.0;
    
    // 解析特征的迭代次数
    nSolveIter 30;
    
    // 松弛迭代次数
    nRelaxIter 5;
    
    // 特征贴合
    nFeatureSnapIter 10;
    
    // 隐式特征贴合
    implicitFeatureSnap false;
    
    // 显式特征贴合
    explicitFeatureSnap true;
    
    // 多区域特征贴合
    multiRegionFeatureSnap false;
}

// *** 边界层网格生成控制参数 ***
addLayersControls
{
    // 使用相对尺寸
    relativeSizes true;
    
    // 指定需要生成边界层的表面
    layers
    {
        cylinder
        {
            nSurfaceLayers 5;  // 边界层层数：5层
        }
    }
    
    // 边界层厚度配置 - 关键参数！
    firstLayerThickness 0.1;    // 第一层厚度相对于当地网格尺寸的比例
    expansionRatio 1.25;         // 层间拉伸比率：每层厚度是前一层的1.18倍

    // 最小厚度限制
    minThickness 0.05;           // 最小边界层厚度相对于当地网格尺寸
    
    // 边界层生成控制
    nGrow 0;                    // 向外增长层数
    featureAngle 150;            // 特征角度阈值
    slipFeatureAngle 30;        // 滑移特征角度
    
    // 迭代控制
    nRelaxIter 5;               // 松弛迭代次数
    nSmoothSurfaceNormals 1;    // 表面法向量平滑次数
    nSmoothNormals 3;           // 法向量平滑次数
    nSmoothThickness 10;        // 厚度平滑次数
    
    // 质量控制参数
    maxFaceThicknessRatio 0.7;      // 面厚度比率上限
    maxThicknessToMedialRatio 0.5;  // 厚度与中线距离比率上限
    minMedialAxisAngle 60;          // 最小中线轴角度
    
    // 层生成迭代控制
    nLayerIter 50;              // 层生成迭代次数
    nRelaxedIter 20;            // 松弛迭代次数
    
    // 缓冲区控制
    nBufferCellsNoExtrude 0;    // 不挤出的缓冲单元数
}

// 网格质量控制
meshQualityControls
{
    // 最大非正交性 - 放宽以便边界层生成
    maxNonOrtho 70;
    
    // 最大边界偏斜度
    maxBoundarySkewness 20;
    
    // 最大内部偏斜度
    maxInternalSkewness 4;
    
    // 最大凹度
    maxConcave 80;
    
    // 最小体积
    minVol 1e-13;
    
    // 最小四面体质量
    minTetQuality 1e-30;
    
    // 最小面积
    minArea -1;
    
    // 最小扭曲度
    minTwist 0.05;
    
    // 最小确定性
    minDeterminant 0.001;
    
    // 最小面平整度
    minFaceWeight 0.05;
    
    // 最小体积比率
    minVolRatio 0.01;
    
    // 最小三角形扭曲度
    minTriangleTwist -1;
    
    // 快照网格质量的迭代次数
    nSmoothScale 4;
    
    // 错误减少
    errorReduction 0.75;
    
    // 松弛迭代次数 - 进一步放宽以利于边界层生成
    relaxed
    {
        maxNonOrtho 75;
        maxBoundarySkewness 25;
        maxInternalSkewness 8;
        maxConcave 85;
        minTetQuality 1e-30;
        minTwist 0.02;
        minFaceWeight 0.02;
        minVolRatio 0.01;
    }
}

// 调试开关
debug 0;

// 合并容忍度
mergeTolerance 1e-6;

// ************************************************************************* //
