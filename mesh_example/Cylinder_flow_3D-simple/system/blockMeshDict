/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2012                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      blockMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

convertToMeters 1;

// Geometry parameters based on documentation
// Domain: x=[0, 2.5], y=[0, 0.41], z=[0, 0.41]
// Cylinder: center=(0.45, 0.205, 0.205), diameter=0.1

vertices
(
    // Bottom face (z=0)
    (0.0    0.0     0.0)    // 0
    (0.4    0.0     0.0)    // 1
    (0.5    0.0     0.0)    // 2
    (2.5    0.0     0.0)    // 3
    (0.0    0.15    0.0)    // 4
    (0.4    0.15    0.0)    // 5
    (0.5    0.15    0.0)    // 6
    (2.5    0.15    0.0)    // 7
    (0.0    0.26    0.0)    // 8
    (0.4    0.26    0.0)    // 9
    (0.5    0.26    0.0)    // 10
    (2.5    0.26    0.0)    // 11
    (0.0    0.41    0.0)    // 12
    (0.4    0.41    0.0)    // 13
    (0.5    0.41    0.0)    // 14
    (2.5    0.41    0.0)    // 15
    
    // Top face (z=0.41)
    (0.0    0.0     0.41)   // 16
    (0.4    0.0     0.41)   // 17
    (0.5    0.0     0.41)   // 18
    (2.5    0.0     0.41)   // 19
    (0.0    0.15    0.41)   // 20
    (0.4    0.15    0.41)   // 21
    (0.5    0.15    0.41)   // 22
    (2.5    0.15    0.41)   // 23
    (0.0    0.26    0.41)   // 24
    (0.4    0.26    0.41)   // 25
    (0.5    0.26    0.41)   // 26
    (2.5    0.26    0.41)   // 27
    (0.0    0.41    0.41)   // 28
    (0.4    0.41    0.41)   // 29
    (0.5    0.41    0.41)   // 30
    (2.5    0.41    0.41)   // 31
);

blocks
(
    // Block 0: inlet bottom left
    hex (0 1 5 4 16 17 21 20) (10 8 10) simpleGrading (1 1 1)
    
    // Block 1: inlet bottom right  
    hex (1 2 6 5 17 18 22 21) (3 8 10) simpleGrading (1 1 1)
    
    // Block 2: middle bottom
    hex (2 3 7 6 18 19 23 22) (50 8 10) simpleGrading (1 1 1)
    
    // Block 3: inlet middle left
    hex (4 5 9 8 20 21 25 24) (10 6 10) simpleGrading (1 1 1)
    
    // Block 4: cylinder region (will be refined)
    hex (5 6 10 9 21 22 26 25) (3 6 10) simpleGrading (1 1 1)
    
    // Block 5: middle middle
    hex (6 7 11 10 22 23 27 26) (50 6 10) simpleGrading (1 1 1)
    
    // Block 6: inlet top left
    hex (8 9 13 12 24 25 29 28) (10 8 10) simpleGrading (1 1 1)
    
    // Block 7: inlet top right
    hex (9 10 14 13 25 26 30 29) (3 8 10) simpleGrading (1 1 1)
    
    // Block 8: middle top
    hex (10 11 15 14 26 27 31 30) (50 8 10) simpleGrading (1 1 1)
);

edges
(
);

boundary
(
    inlet
    {
        type patch;
        faces
        (
            (0 4 20 16)
            (4 8 24 20)
            (8 12 28 24)
        );
    }
    
    outlet
    {
        type patch;
        faces
        (
            (3 19 23 7)
            (7 23 27 11)
            (11 27 31 15)
        );
    }
    
    topWall
    {
        type wall;
        faces
        (
            (12 13 29 28)
            (13 14 30 29)
            (14 15 31 30)
        );
    }
    
    bottomWall
    {
        type wall;
        faces
        (
            (0 16 17 1)
            (1 17 18 2)
            (2 18 19 3)
        );
    }
    
    front
    {
        type wall;
        faces
        (
            (0 1 5 4)
            (1 2 6 5)
            (2 3 7 6)
            (4 5 9 8)
            (5 6 10 9)
            (6 7 11 10)
            (8 9 13 12)
            (9 10 14 13)
            (10 11 15 14)
        );
    }
    
    back
    {
        type wall;
        faces
        (
            (16 20 21 17)
            (17 21 22 18)
            (18 22 23 19)
            (20 24 25 21)
            (21 25 26 22)
            (22 26 27 23)
            (24 28 29 25)
            (25 29 30 26)
            (26 30 31 27)
        );
    }
);

mergePatchPairs
(
);

// ************************************************************************* //
