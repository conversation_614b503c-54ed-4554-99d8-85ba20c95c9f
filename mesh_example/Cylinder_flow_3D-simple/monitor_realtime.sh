#!/bin/bash

# Real-time monitoring script for Cd/Cl values
echo "=== Real-time Cd/Cl Monitoring for Re=100 Cylinder Flow ==="
echo "Time        Cd          Cl          CFL_max"
echo "============================================="

# Function to extract latest values
extract_latest() {
    if [ -f "postProcessing/forceCoeffs/0/forceCoeffs.dat" ]; then
        tail -1 postProcessing/forceCoeffs/0/forceCoeffs.dat | awk '{printf "%.4f      %.6f    %.6f", $1, $2, $3}'
    else
        printf "No data yet..."
    fi
}

# Function to extract CFL from log
extract_cfl() {
    if [ -f "log.pimpleFoam" ]; then
        tail -20 log.pimpleFoam | grep "Courant Number" | tail -1 | awk '{print $NF}'
    else
        echo "N/A"
    fi
}

# Monitor loop
while true; do
    latest=$(extract_latest)
    cfl=$(extract_cfl)
    if [ "$cfl" != "N/A" ] && [ "$cfl" != "" ]; then
        printf "\r$latest    $cfl"
    else
        printf "\r$latest    N/A    "
    fi
    sleep 2
done
