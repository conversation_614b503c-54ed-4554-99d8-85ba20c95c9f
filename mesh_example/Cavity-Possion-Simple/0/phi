/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  v2406
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    object      phi;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 0 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    lid
    {
        type            codedFixedValue;
        value           uniform 0;
        name            sinusoidalBC;
        code
        #{
            const vectorField& Cf = patch().Cf();
            scalarField& field = *this;
            
            forAll(Cf, faceI)
            {
                scalar x = Cf[faceI].x();
                field[faceI] = 2.0 * sin(2.0 * M_PI * x);
            }
        #};
    }

    fixedWalls
    {
        type            fixedValue;
        value           uniform 0;
    }

    frontAndBack
    {
        type            fixedValue;
        value           uniform 0;
    }
}

// ************************************************************************* //
