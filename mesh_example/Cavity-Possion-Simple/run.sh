#!/bin/bash

# Source the OpenFOAM environment if it's not already sourced
# You might need to change this path depending on your OpenFOAM installation
if [ -z "$WM_PROJECT" ]; then
    source /usr/lib/openfoam/openfoam2406/etc/bashrc
fi

# Clean the case
# foamCleanTutorials

# Compile the custom solver
echo "Compiling poissonSolver..."
wmake

# Run the solver
echo "Running poissonSolver..."
poissonSolver
