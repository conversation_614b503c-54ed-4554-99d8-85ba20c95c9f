/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     |
    \\  /    A nd           | www.openfoam.com
     \\/     M anipulation  |
-------------------------------------------------------------------------------
    Copyright (C) 2019-2021 OpenCFD Ltd.
    Copyright (C) YEAR AUTHOR, AFFILIATION
-------------------------------------------------------------------------------
License
    This file is part of OpenFOAM.

    OpenFOAM is free software: you can redistribute it and/or modify it
    under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    OpenFOAM is distributed in the hope that it will be useful, but WITHOUT
    ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
    FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
    for more details.

    You should have received a copy of the GNU General Public License
    along with OpenFOAM.  If not, see <http://www.gnu.org/licenses/>.

\*---------------------------------------------------------------------------*/

#include "fixedValueFvPatchFieldTemplate.H"
#include "addToRunTimeSelectionTable.H"
#include "fvPatchFieldMapper.H"
#include "volFields.H"
#include "surfaceFields.H"
#include "unitConversion.H"
#include "PatchFunction1.H"

//{{{ begin codeInclude

//}}} end codeInclude


// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

namespace Foam
{

// * * * * * * * * * * * * * * * Local Functions * * * * * * * * * * * * * * //

//{{{ begin localCode

//}}} end localCode


// * * * * * * * * * * * * * * * Global Functions  * * * * * * * * * * * * * //

// dynamicCode:
// SHA1 = 18996b6a1c21fef1aebc8810a93da7dd9cb4d25c
//
// unique function name that can be checked if the correct library version
// has been loaded
extern "C" void sinusoidalBC_18996b6a1c21fef1aebc8810a93da7dd9cb4d25c(bool load)
{
    if (load)
    {
        // Code that can be explicitly executed after loading
    }
    else
    {
        // Code that can be explicitly executed before unloading
    }
}

// * * * * * * * * * * * * * * Static Data Members * * * * * * * * * * * * * //

makeRemovablePatchTypeField
(
    fvPatchScalarField,
    sinusoidalBCFixedValueFvPatchScalarField
);

} // End namespace Foam


// * * * * * * * * * * * * * * * * Constructors  * * * * * * * * * * * * * * //

Foam::
sinusoidalBCFixedValueFvPatchScalarField::
sinusoidalBCFixedValueFvPatchScalarField
(
    const fvPatch& p,
    const DimensionedField<scalar, volMesh>& iF
)
:
    parent_bctype(p, iF)
{
    if (false)
    {
        printMessage("Construct sinusoidalBC : patch/DimensionedField");
    }
}


Foam::
sinusoidalBCFixedValueFvPatchScalarField::
sinusoidalBCFixedValueFvPatchScalarField
(
    const sinusoidalBCFixedValueFvPatchScalarField& rhs,
    const fvPatch& p,
    const DimensionedField<scalar, volMesh>& iF,
    const fvPatchFieldMapper& mapper
)
:
    parent_bctype(rhs, p, iF, mapper)
{
    if (false)
    {
        printMessage("Construct sinusoidalBC : patch/DimensionedField/mapper");
    }
}


Foam::
sinusoidalBCFixedValueFvPatchScalarField::
sinusoidalBCFixedValueFvPatchScalarField
(
    const fvPatch& p,
    const DimensionedField<scalar, volMesh>& iF,
    const dictionary& dict
)
:
    parent_bctype(p, iF, dict)
{
    if (false)
    {
        printMessage("Construct sinusoidalBC : patch/dictionary");
    }
}


Foam::
sinusoidalBCFixedValueFvPatchScalarField::
sinusoidalBCFixedValueFvPatchScalarField
(
    const sinusoidalBCFixedValueFvPatchScalarField& rhs
)
:
    parent_bctype(rhs),
    dictionaryContent(rhs)
{
    if (false)
    {
        printMessage("Copy construct sinusoidalBC");
    }
}


Foam::
sinusoidalBCFixedValueFvPatchScalarField::
sinusoidalBCFixedValueFvPatchScalarField
(
    const sinusoidalBCFixedValueFvPatchScalarField& rhs,
    const DimensionedField<scalar, volMesh>& iF
)
:
    parent_bctype(rhs, iF)
{
    if (false)
    {
        printMessage("Construct sinusoidalBC : copy/DimensionedField");
    }
}


// * * * * * * * * * * * * * * * * Destructor  * * * * * * * * * * * * * * * //

Foam::
sinusoidalBCFixedValueFvPatchScalarField::
~sinusoidalBCFixedValueFvPatchScalarField()
{
    if (false)
    {
        printMessage("Destroy sinusoidalBC");
    }
}


// * * * * * * * * * * * * * * * Member Functions  * * * * * * * * * * * * * //

void
Foam::
sinusoidalBCFixedValueFvPatchScalarField::updateCoeffs()
{
    if (this->updated())
    {
        return;
    }

    if (false)
    {
        printMessage("updateCoeffs sinusoidalBC");
    }

//{{{ begin code
    #line 29 "/mnt/lv/litianyu/mycode/TorchFVM/mesh_example/Cavity-Possion-Simple/0/phi/boundaryField/lid"
const vectorField& Cf = patch().Cf();
            scalarField& field = *this;
            
            forAll(Cf, faceI)
            {
                scalar x = Cf[faceI].x();
                field[faceI] = 2.0 * sin(2.0 * M_PI * x);
            }
//}}} end code

    this->parent_bctype::updateCoeffs();
}


// ************************************************************************* //

