# 泊松方程求解器 - OpenFOAM Case

本case已经修改为求解泊松方程，与Python代码中的设置保持一致。

## 方程形式
```
∇²φ = Qv
```
其中：
- φ: 待求解的标量场
- Qv: 源项 (常数 = 1)
- 扩散系数: DT = 0.01

## 边界条件
与Python代码保持一致：

1. **顶部边界 (lid)**: 
   - 类型: 狄利克雷边界条件
   - 值: φ = 2*sin(2π*x)
   - 实现: 使用codedFixedValue边界条件

2. **墙壁边界 (fixedWalls + frontAndBack)**:
   - 类型: 狄利克雷边界条件  
   - 值: φ = 0
   - 实现: 使用fixedValue边界条件

## 主要修改文件

### 1. system/controlDict
- 求解器: poissonSolver (自定义)
- 时间设置: 稳态求解

### 2. 0/phi
- 新增标量场文件
- 设置边界条件与Python代码一致

### 3. constant/transportProperties  
- 扩散系数: DT = 0.01

### 4. system/fvSchemes
- 时间格式: steadyState
- 拉普拉斯格式: Gaus<PERSON> linear corrected

### 5. system/fvSolution
- 求解器: PCG with DIC preconditioner
- 容差: 1e-07 (与Python代码一致)

### 6. poissonSolver.C
- 自定义求解器源代码
- 求解方程: fvm::laplacian(DT, phi) == Qv

## 运行方法

### 方法1: 使用Allrun脚本 (推荐)
```bash
./Allrun
```

### 方法2: 使用run.sh脚本
```bash
./run.sh
```

### 方法3: 手动运行
```bash
# 编译求解器
wmake

# 生成网格
blockMesh

# 运行求解器
poissonSolver
```

## 清理
```bash
./Allclean
```

## 结果文件
求解完成后，结果将保存在时间目录中，可以使用ParaView查看φ场的分布。

## 与Python代码的对应关系
- Python中的`cpd_cell_value`: 对应OpenFOAM中的φ场
- Python中的`diff_coeff = 0.01`: 对应OpenFOAM中的DT = 0.01
- Python中的`Qv = 1`: 对应OpenFOAM中的源项Qv = 1
- Python中的边界条件设置: 完全对应OpenFOAM中的边界条件设置

## 验证结果
运行`python3 validate_results.py`可以查看求解结果的详细分析：

- **网格规模**: 8000个单元 (20×20×20)
- **求解收敛**: 25次迭代，最终残差4.95e-08
- **φ场范围**: [-5.614, 1.192]
- **边界条件**: 正确应用了正弦函数边界条件和零值边界条件

## 成功验证项目
✓ 方程形式一致 (泊松方程)
✓ 扩散系数一致 (0.01)
✓ 源项一致 (常数1)
✓ 边界条件一致 (顶部正弦，墙壁为零)
✓ 求解器收敛良好
✓ 结果合理且物理意义正确
