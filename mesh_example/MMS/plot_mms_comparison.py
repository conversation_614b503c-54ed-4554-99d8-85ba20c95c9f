#!/usr/bin/env python3
"""
MMS (Method of Manufactured Solutions) Validation Results Comparison
Plots L2 error vs dx for both Python and OpenFOAM solvers
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def load_mms_data(filename):
    """Load MMS validation data from JSON file"""
    with open(filename, 'r') as f:
        data = json.load(f)
    
    # Extract dx and L2_error
    dx_values = [item['dx'] for item in data]
    l2_errors = [item['L2_error'] for item in data]
    
    return np.array(dx_values), np.array(l2_errors)

def plot_mms_comparison():
    """Create MMS comparison plot"""
    # Load data
    python_dx, python_l2 = load_mms_data('python_mms_validation_results.json')
    openfoam_dx, openfoam_l2 = load_mms_data('openfoam_mms_validation_results.json')
    
    # Create figure with high DPI for better quality
    plt.figure(figsize=(10, 8), dpi=300)
    
    # Plot data points and lines
    plt.loglog(python_dx, python_l2, 'o-', linewidth=2, markersize=8, 
               label='Python TorchFVM', color='blue', markerfacecolor='lightblue')
    plt.loglog(openfoam_dx, openfoam_l2, 's-', linewidth=2, markersize=8, 
               label='OpenFOAM', color='red', markerfacecolor='lightcoral')
    
    # Add theoretical convergence lines for reference
    # For second-order accuracy: error ∝ dx²
    dx_theory = np.logspace(np.log10(0.02), np.log10(0.12), 100)
    
    # Fit theoretical lines to the data
    # Use the first data point to normalize
    python_theory = python_l2[0] * (dx_theory / python_dx[0])**2
    openfoam_theory = openfoam_l2[0] * (dx_theory / openfoam_dx[0])**2
    
    plt.loglog(dx_theory, python_theory, '--', alpha=0.7, color='blue', 
               label='2nd order slope (Python)')
    plt.loglog(dx_theory, openfoam_theory, '--', alpha=0.7, color='red', 
               label='2nd order slope (OpenFOAM)')
    
    # Calculate and display convergence rates
    def calculate_convergence_rate(dx, errors):
        """Calculate convergence rate using least squares fit in log space"""
        log_dx = np.log(dx)
        log_errors = np.log(errors)
        slope, _ = np.polyfit(log_dx, log_errors, 1)
        return slope
    
    python_rate = calculate_convergence_rate(python_dx, python_l2)
    openfoam_rate = calculate_convergence_rate(openfoam_dx, openfoam_l2)
    
    # Formatting and labels
    plt.xlabel('Grid Spacing (dx)', fontsize=14, fontweight='bold')
    plt.ylabel('L2 Error', fontsize=14, fontweight='bold')
    plt.title('MMS Validation: Convergence Analysis\nPoisson Equation Solver Comparison', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Grid and styling
    plt.grid(True, which="both", ls="-", alpha=0.3)
    plt.legend(fontsize=12, loc='upper left')
    
    # Add convergence rate annotations
    plt.text(0.02, 1e-4, f'Python convergence rate: {python_rate:.2f}', 
             fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    plt.text(0.02, 5e-5, f'OpenFOAM convergence rate: {openfoam_rate:.2f}', 
             fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7))
    
    # Set axis limits for better visualization
    plt.xlim(0.02, 0.12)
    plt.ylim(1e-5, 1e-3)
    
    # Improve layout
    plt.tight_layout()
    
    # Save the plot
    plt.savefig('mms_convergence_comparison.png', dpi=300, bbox_inches='tight')
    plt.savefig('mms_convergence_comparison.pdf', bbox_inches='tight')
    
    print("MMS Convergence Analysis:")
    print(f"Python TorchFVM convergence rate: {python_rate:.3f}")
    print(f"OpenFOAM convergence rate: {openfoam_rate:.3f}")
    print(f"Theoretical 2nd order rate: 2.000")
    print("\nPlots saved as:")
    print("- mms_convergence_comparison.png")
    print("- mms_convergence_comparison.pdf")
    
    # Show the plot
    plt.show()

def print_data_summary():
    """Print summary of the MMS data"""
    python_dx, python_l2 = load_mms_data('python_mms_validation_results.json')
    openfoam_dx, openfoam_l2 = load_mms_data('openfoam_mms_validation_results.json')
    
    print("\nData Summary:")
    print("=" * 60)
    print(f"{'Grid Size':<12} {'dx':<10} {'Python L2':<12} {'OpenFOAM L2':<12} {'Ratio':<8}")
    print("-" * 60)
    
    for i in range(len(python_dx)):
        grid_size = f"{int(1/python_dx[i])}³"
        ratio = python_l2[i] / openfoam_l2[i]
        print(f"{grid_size:<12} {python_dx[i]:<10.3f} {python_l2[i]:<12.2e} "
              f"{openfoam_l2[i]:<12.2e} {ratio:<8.3f}")

if __name__ == "__main__":
    # Change to the script directory
    script_dir = Path(__file__).parent
    import os
    os.chdir(script_dir)
    
    print("MMS Validation Results Comparison")
    print("=" * 40)
    
    # Print data summary
    print_data_summary()
    
    # Create the comparison plot
    plot_mms_comparison()
