#!/usr/bin/env python3
"""
Python MMS验证 - 使用优化的稀疏求解器
运行4个网格密度的Python求解器，计算误差并保存结果

求解器: src/Possion_solve_optmised.py (支持稀疏矩阵求解)
环境: conda环境 FVGN-pt2.7-sp
"""

import os
import sys
sys.path.append('mesh_example/MMS')
import subprocess
import json
import re
import numpy as np
import time

def check_and_convert_h5(case_path):
    """检查h5文件是否存在，如果不存在则运行转换脚本"""
    case_name = os.path.basename(case_path)
    h5_file = os.path.join(case_path, f"{case_name}.h5")
    
    if os.path.exists(h5_file):
        print(f"  h5文件已存在: {h5_file}")
        return True
    
    print(f"  h5文件不存在，开始转换...")
    
    # 运行转换脚本 - 使用conda环境FVGN-pt2.7-sp
    try:
        # 使用相对路径
        parser_path = '../../src/Parse_mesh/parse_openfoam_refactored.py'
        conda_python = '/mnt/lv/litianyu/miniconda3/envs/FVGN-pt2.7-sp/bin/python'

        result = subprocess.run([
            conda_python, parser_path, case_path
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode != 0:
            print(f"    转换失败: {result.stderr}")
            return False
        
        # 检查h5文件是否生成
        if os.path.exists(h5_file):
            print(f"    转换成功: {h5_file}")
            return True
        else:
            print(f"    转换失败: h5文件未生成")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"    转换超时")
        return False
    except Exception as e:
        print(f"    转换错误: {e}")
        return False

def run_python_solver(case_path):
    """运行Python求解器 - 使用conda环境FVGN-pt2.7-sp"""
    case_name = os.path.basename(case_path)
    print(f"运行Python求解器: {case_name}")

    try:
        start_time = time.time()

        # 使用相对路径
        solver_path = 'src/Possion_solve_optmised.py'

        # 直接使用conda环境的Python解释器
        conda_python = '/mnt/lv/litianyu/miniconda3/envs/FVGN-pt2.7-sp/bin/python'

        result = subprocess.run([
            conda_python, solver_path, case_path
        ], capture_output=True, text=True, timeout=600)
        end_time = time.time()
        solve_time = end_time - start_time

        if result.returncode != 0:
            print(f"  求解失败: {result.stderr}")
            return None, None

        print(f"  求解完成，耗时: {solve_time:.3f}秒")
        return result.stdout, solve_time

    except subprocess.TimeoutExpired:
        print(f"  求解超时")
        return None, None
    except Exception as e:
        print(f"  求解错误: {e}")
        return None, None

def parse_python_output(output, case_name, n, solve_time):
    """解析Python求解器的输出，提取误差信息"""
    try:
        lines = output.split('\n')
        
        L1_error = None
        L2_error = None
        Linf_error = None
        num_cells = None
        dx = None
        
        for line in lines:
            if "L1 误差:" in line:
                match = re.search(r'L1 误差:\s+([\d\.e\-\+]+)', line)
                if match:
                    L1_error = float(match.group(1))
            elif "L2 误差:" in line:
                match = re.search(r'L2 误差:\s+([\d\.e\-\+]+)', line)
                if match:
                    L2_error = float(match.group(1))
            elif "L∞ 误差:" in line:
                match = re.search(r'L∞ 误差:\s+([\d\.e\-\+]+)', line)
                if match:
                    Linf_error = float(match.group(1))
            elif "网格单元数:" in line:
                match = re.search(r'网格单元数:\s+(\d+)', line)
                if match:
                    num_cells = int(match.group(1))
            elif "特征网格间距 dx:" in line:
                match = re.search(r'特征网格间距 dx:\s+([\d\.e\-\+]+)', line)
                if match:
                    dx = float(match.group(1))
        
        if all(x is not None for x in [L1_error, L2_error, Linf_error, num_cells, dx]):
            return {
                'case_name': case_name,
                'n': n,
                'num_cells': num_cells,
                'dx': dx,
                'L1_error': L1_error,
                'L2_error': L2_error,
                'Linf_error': Linf_error,
                'solve_time': solve_time
            }
        else:
            print(f"  警告: 无法解析所有误差指标")
            print(f"  缺失: L1={L1_error is None}, L2={L2_error is None}, Linf={Linf_error is None}, cells={num_cells is None}, dx={dx is None}")
            return None
            
    except Exception as e:
        print(f"  解析输出错误: {e}")
        return None

def calculate_convergence_order(results, error_type='L2_error'):
    """计算收敛阶数"""
    if len(results) < 2:
        return []
    
    orders = []
    for i in range(1, len(results)):
        dx1 = results[i-1]['dx']
        dx2 = results[i]['dx']
        error1 = results[i-1][error_type]
        error2 = results[i][error_type]
        
        if error1 > 0 and error2 > 0:
            order = np.log(error2/error1) / np.log(dx2/dx1)
            orders.append(order)
        else:
            orders.append(np.nan)
    
    return orders

def print_results_summary(results):
    """打印结果总结"""
    print("\n" + "="*80)
    print("Python求解器 MMS验证结果")
    print("="*80)
    print(f"{'案例':<15} {'单元数':<8} {'dx':<10} {'L1误差':<12} {'L2误差':<12} {'L∞误差':<12} {'时间(s)':<8}")
    print("-"*80)
    
    for result in results:
        print(f"{result['case_name']:<15} {result['num_cells']:<8} {result['dx']:<10.6f} "
              f"{result['L1_error']:<12.6e} {result['L2_error']:<12.6e} "
              f"{result['Linf_error']:<12.6e} {result['solve_time']:<8.3f}")
    
    # 计算收敛阶数
    L1_orders = calculate_convergence_order(results, 'L1_error')
    L2_orders = calculate_convergence_order(results, 'L2_error')
    Linf_orders = calculate_convergence_order(results, 'Linf_error')
    
    print(f"\n收敛阶数分析:")
    print(f"{'网格':<10} {'L1阶数':<8} {'L2阶数':<8} {'L∞阶数':<8}")
    print("-"*40)
    
    for i, result in enumerate(results):
        L1_order_str = f"{L1_orders[i-1]:.2f}" if i > 0 and i-1 < len(L1_orders) else "-"
        L2_order_str = f"{L2_orders[i-1]:.2f}" if i > 0 and i-1 < len(L2_orders) else "-"
        Linf_order_str = f"{Linf_orders[i-1]:.2f}" if i > 0 and i-1 < len(Linf_orders) else "-"
        
        print(f"{result['n']}³{'':<6} {L1_order_str:<8} {L2_order_str:<8} {Linf_order_str:<8}")
    
    # 平均收敛阶数
    valid_L1_orders = [o for o in L1_orders if not np.isnan(o)]
    valid_L2_orders = [o for o in L2_orders if not np.isnan(o)]
    valid_Linf_orders = [o for o in Linf_orders if not np.isnan(o)]
    
    print(f"\n平均收敛阶数:")
    if valid_L1_orders:
        print(f"  L1误差:  {np.mean(valid_L1_orders):.2f} ± {np.std(valid_L1_orders):.2f}")
    if valid_L2_orders:
        print(f"  L2误差:  {np.mean(valid_L2_orders):.2f} ± {np.std(valid_L2_orders):.2f}")
    if valid_Linf_orders:
        print(f"  L∞误差:  {np.mean(valid_Linf_orders):.2f} ± {np.std(valid_Linf_orders):.2f}")

def main():
    """主函数"""
    # 测试案例配置 - 使用相对路径
    cases = [
        ("mesh_example/MMS/MMS-10x10x10", 10),
        ("mesh_example/MMS/MMS-20x20x20", 20),
        ("mesh_example/MMS/MMS-30x30x30", 30),
        ("mesh_example/MMS/MMS-40x40x40", 40)
    ]
    
    print("Python求解器 MMS验证")
    print("解析解: φ(x,y,z) = 0.1 × sin(πx) × sin(πy) × sin(πz)")
    print("="*60)
    
    results = []
    
    for case_path, n in cases:
        if not os.path.exists(case_path):
            print(f"警告: 案例目录不存在 {case_path}")
            continue
        
        case_name = os.path.basename(case_path)
        print(f"\n处理案例: {case_name}")
        
        # 检查h5文件是否存在
        case_name_h5 = os.path.join(case_path, "h5", f"{case_name}.h5")
        if not os.path.exists(case_name_h5):
            print(f"  跳过案例 {case_name}: h5文件不存在 {case_name_h5}")
            continue
        else:
            print(f"  h5文件已存在: {case_name_h5}")
        
        # 运行Python求解器
        output, solve_time = run_python_solver(case_path)
        if output is None:
            print(f"  跳过案例 {case_name}: 求解失败")
            continue
        
        # 解析输出并提取误差
        error_data = parse_python_output(output, case_name, n, solve_time)
        if error_data is None:
            print(f"  跳过案例 {case_name}: 无法解析误差")
            continue
        
        results.append(error_data)
        print(f"  ✓ 案例 {case_name} 完成")
        print(f"    L1误差: {error_data['L1_error']:.6e}")
        print(f"    L2误差: {error_data['L2_error']:.6e}")
        print(f"    L∞误差: {error_data['Linf_error']:.6e}")
    
    if not results:
        print("错误: 没有成功的测试案例!")
        return
    
    # 打印结果总结
    print_results_summary(results)
    
    # 保存结果到JSON文件 - 保存到指定的绝对路径
    output_file = 'mesh_example/MMS/python_mms_validation_results.json'
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n结果已保存到: {output_file}")
    print("验证完成!")

if __name__ == "__main__":
    main()
