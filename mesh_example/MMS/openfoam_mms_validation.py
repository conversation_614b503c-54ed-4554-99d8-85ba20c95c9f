#!/usr/bin/env python3
"""
OpenFOAM MMS验证 - 完整版本
运行4个网格密度的OpenFOAM求解器，计算误差并保存结果
"""

import os
import subprocess
import json
import re
import numpy as np
import time

def run_openfoam_case(case_path):
    """运行OpenFOAM案例"""
    case_name = os.path.basename(case_path)
    print(f"运行案例: {case_name}")
    
    original_cwd = os.getcwd()
    os.chdir(case_path)
    
    try:
        start_time = time.time()
        result = subprocess.run(['poissonSolver'], capture_output=True, text=True)
        end_time = time.time()
        solve_time = end_time - start_time
        
        if result.returncode != 0:
            print(f"  错误: {result.stderr}")
            return None
        
        print(f"  求解完成，耗时: {solve_time:.3f}秒")
        return solve_time
        
    except Exception as e:
        print(f"  错误: {e}")
        return None
    finally:
        os.chdir(original_cwd)

def parse_openfoam_scalar_field(file_path):
    """解析OpenFOAM标量场文件"""
    with open(file_path, 'r') as f:
        content = f.read()
    
    pattern = r'internalField\s+nonuniform\s+List<scalar>\s*\n(\d+)\s*\n\(\s*(.*?)\s*\)\s*;'
    match = re.search(pattern, content, re.DOTALL)
    
    if not match:
        raise ValueError("无法解析internalField")
    
    num_cells = int(match.group(1))
    data_str = match.group(2)
    
    values = []
    lines = data_str.strip().split('\n')
    for line in lines:
        line = line.strip()
        if line:
            try:
                values.append(float(line))
            except ValueError:
                continue
    
    return np.array(values)

def calculate_analytical_solution(n):
    """计算解析解: phi = 0.1 * sin(pi*x) * sin(pi*y) * sin(pi*z)"""
    x = np.linspace(0.5/n, 1-0.5/n, n)  # 单元中心
    y = np.linspace(0.5/n, 1-0.5/n, n)
    z = np.linspace(0.5/n, 1-0.5/n, n)
    
    analytical = []
    for k in range(n):
        for j in range(n):
            for i in range(n):
                phi = 0.1 * np.sin(np.pi*x[i]) * np.sin(np.pi*y[j]) * np.sin(np.pi*z[k])
                analytical.append(phi)
    
    return np.array(analytical)

def calculate_errors(case_path, n):
    """计算指定案例的误差"""
    case_name = os.path.basename(case_path)
    print(f"  分析误差: {case_name}")
    
    # 读取数值解
    phi_file = os.path.join(case_path, "1", "phi")
    if not os.path.exists(phi_file):
        print(f"    警告: 未找到结果文件 {phi_file}")
        return None
    
    try:
        numerical_values = parse_openfoam_scalar_field(phi_file)
        analytical_values = calculate_analytical_solution(n)
        
        if len(numerical_values) != len(analytical_values):
            print(f"    警告: 数组长度不匹配 - 数值解: {len(numerical_values)}, 解析解: {len(analytical_values)}")
            return None
        
        # 计算误差
        error = numerical_values - analytical_values
        L1_error = np.mean(np.abs(error))
        L2_error = np.sqrt(np.mean(error**2))
        Linf_error = np.max(np.abs(error))
        
        # 计算网格间距
        dx = 1.0 / n
        
        result = {
            'case_name': case_name,
            'n': n,
            'num_cells': len(numerical_values),
            'dx': dx,
            'L1_error': L1_error,
            'L2_error': L2_error,
            'Linf_error': Linf_error
        }
        
        print(f"    L1误差: {L1_error:.6e}")
        print(f"    L2误差: {L2_error:.6e}")
        print(f"    L∞误差: {Linf_error:.6e}")
        
        return result
        
    except Exception as e:
        print(f"    错误: {e}")
        return None

def calculate_convergence_order(results, error_type='L2_error'):
    """计算收敛阶数"""
    if len(results) < 2:
        return []
    
    orders = []
    for i in range(1, len(results)):
        dx1 = results[i-1]['dx']
        dx2 = results[i]['dx']
        error1 = results[i-1][error_type]
        error2 = results[i][error_type]
        
        if error1 > 0 and error2 > 0:
            order = np.log(error2/error1) / np.log(dx2/dx1)
            orders.append(order)
        else:
            orders.append(np.nan)
    
    return orders

def print_results_summary(results):
    """打印结果总结"""
    print("\n" + "="*80)
    print("OpenFOAM MMS验证结果")
    print("="*80)
    print(f"{'案例':<15} {'单元数':<8} {'dx':<10} {'L1误差':<12} {'L2误差':<12} {'L∞误差':<12}")
    print("-"*80)
    
    for result in results:
        print(f"{result['case_name']:<15} {result['num_cells']:<8} {result['dx']:<10.6f} "
              f"{result['L1_error']:<12.6e} {result['L2_error']:<12.6e} "
              f"{result['Linf_error']:<12.6e}")
    
    # 计算收敛阶数
    L1_orders = calculate_convergence_order(results, 'L1_error')
    L2_orders = calculate_convergence_order(results, 'L2_error')
    Linf_orders = calculate_convergence_order(results, 'Linf_error')
    
    print(f"\n收敛阶数分析:")
    print(f"{'网格':<10} {'L1阶数':<8} {'L2阶数':<8} {'L∞阶数':<8}")
    print("-"*40)
    
    for i, result in enumerate(results):
        L1_order_str = f"{L1_orders[i-1]:.2f}" if i > 0 and i-1 < len(L1_orders) else "-"
        L2_order_str = f"{L2_orders[i-1]:.2f}" if i > 0 and i-1 < len(L2_orders) else "-"
        Linf_order_str = f"{Linf_orders[i-1]:.2f}" if i > 0 and i-1 < len(Linf_orders) else "-"
        
        print(f"{result['n']}³{'':<6} {L1_order_str:<8} {L2_order_str:<8} {Linf_order_str:<8}")
    
    # 平均收敛阶数
    valid_L1_orders = [o for o in L1_orders if not np.isnan(o)]
    valid_L2_orders = [o for o in L2_orders if not np.isnan(o)]
    valid_Linf_orders = [o for o in Linf_orders if not np.isnan(o)]
    
    print(f"\n平均收敛阶数:")
    if valid_L1_orders:
        print(f"  L1误差:  {np.mean(valid_L1_orders):.2f} ± {np.std(valid_L1_orders):.2f}")
    if valid_L2_orders:
        print(f"  L2误差:  {np.mean(valid_L2_orders):.2f} ± {np.std(valid_L2_orders):.2f}")
    if valid_Linf_orders:
        print(f"  L∞误差:  {np.mean(valid_Linf_orders):.2f} ± {np.std(valid_Linf_orders):.2f}")

def main():
    """主函数"""
    # 测试案例配置
    cases = [
        ("mesh_example/MMS/MMS-10x10x10", 10),
        ("mesh_example/MMS/MMS-20x20x20", 20),
        ("mesh_example/MMS/MMS-30x30x30", 30),
        ("mesh_example/MMS/MMS-40x40x40", 40)
    ]
    
    print("OpenFOAM MMS验证")
    print("解析解: φ(x,y,z) = 0.1 × sin(πx) × sin(πy) × sin(πz)")
    print("="*60)
    
    results = []
    
    for case_path, n in cases:
        if not os.path.exists(case_path):
            print(f"警告: 案例目录不存在 {case_path}")
            continue
        
        # 运行OpenFOAM求解器
        solve_time = run_openfoam_case(case_path)
        if solve_time is None:
            continue
        
        # 计算误差
        error_data = calculate_errors(case_path, n)
        if error_data is None:
            continue
        
        error_data['solve_time'] = solve_time
        results.append(error_data)
    
    if not results:
        print("错误: 没有成功的测试案例!")
        return
    
    # 打印结果总结
    print_results_summary(results)
    
    # 保存结果到JSON文件
    output_file = 'mesh_example/MMS/openfoam_mms_validation_results.json'
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n结果已保存到: {output_file}")
    print("验证完成!")

if __name__ == "__main__":
    main()
