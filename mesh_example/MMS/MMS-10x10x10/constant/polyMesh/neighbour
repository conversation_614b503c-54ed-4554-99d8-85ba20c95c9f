/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2406                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    note        "nPoints:1331  nCells:1000  nFaces:3300  nInternalFaces:2700";
    class       labelList;
    location    "constant/polyMesh";
    object      neighbour;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //


2700
(
1
10
100
2
11
101
3
12
102
4
13
103
5
14
104
6
15
105
7
16
106
8
17
107
9
18
108
19
109
11
20
110
12
21
111
13
22
112
14
23
113
15
24
114
16
25
115
17
26
116
18
27
117
19
28
118
29
119
21
30
120
22
31
121
23
32
122
24
33
123
25
34
124
26
35
125
27
36
126
28
37
127
29
38
128
39
129
31
40
130
32
41
131
33
42
132
34
43
133
35
44
134
36
45
135
37
46
136
38
47
137
39
48
138
49
139
41
50
140
42
51
141
43
52
142
44
53
143
45
54
144
46
55
145
47
56
146
48
57
147
49
58
148
59
149
51
60
150
52
61
151
53
62
152
54
63
153
55
64
154
56
65
155
57
66
156
58
67
157
59
68
158
69
159
61
70
160
62
71
161
63
72
162
64
73
163
65
74
164
66
75
165
67
76
166
68
77
167
69
78
168
79
169
71
80
170
72
81
171
73
82
172
74
83
173
75
84
174
76
85
175
77
86
176
78
87
177
79
88
178
89
179
81
90
180
82
91
181
83
92
182
84
93
183
85
94
184
86
95
185
87
96
186
88
97
187
89
98
188
99
189
91
190
92
191
93
192
94
193
95
194
96
195
97
196
98
197
99
198
199
101
110
200
102
111
201
103
112
202
104
113
203
105
114
204
106
115
205
107
116
206
108
117
207
109
118
208
119
209
111
120
210
112
121
211
113
122
212
114
123
213
115
124
214
116
125
215
117
126
216
118
127
217
119
128
218
129
219
121
130
220
122
131
221
123
132
222
124
133
223
125
134
224
126
135
225
127
136
226
128
137
227
129
138
228
139
229
131
140
230
132
141
231
133
142
232
134
143
233
135
144
234
136
145
235
137
146
236
138
147
237
139
148
238
149
239
141
150
240
142
151
241
143
152
242
144
153
243
145
154
244
146
155
245
147
156
246
148
157
247
149
158
248
159
249
151
160
250
152
161
251
153
162
252
154
163
253
155
164
254
156
165
255
157
166
256
158
167
257
159
168
258
169
259
161
170
260
162
171
261
163
172
262
164
173
263
165
174
264
166
175
265
167
176
266
168
177
267
169
178
268
179
269
171
180
270
172
181
271
173
182
272
174
183
273
175
184
274
176
185
275
177
186
276
178
187
277
179
188
278
189
279
181
190
280
182
191
281
183
192
282
184
193
283
185
194
284
186
195
285
187
196
286
188
197
287
189
198
288
199
289
191
290
192
291
193
292
194
293
195
294
196
295
197
296
198
297
199
298
299
201
210
300
202
211
301
203
212
302
204
213
303
205
214
304
206
215
305
207
216
306
208
217
307
209
218
308
219
309
211
220
310
212
221
311
213
222
312
214
223
313
215
224
314
216
225
315
217
226
316
218
227
317
219
228
318
229
319
221
230
320
222
231
321
223
232
322
224
233
323
225
234
324
226
235
325
227
236
326
228
237
327
229
238
328
239
329
231
240
330
232
241
331
233
242
332
234
243
333
235
244
334
236
245
335
237
246
336
238
247
337
239
248
338
249
339
241
250
340
242
251
341
243
252
342
244
253
343
245
254
344
246
255
345
247
256
346
248
257
347
249
258
348
259
349
251
260
350
252
261
351
253
262
352
254
263
353
255
264
354
256
265
355
257
266
356
258
267
357
259
268
358
269
359
261
270
360
262
271
361
263
272
362
264
273
363
265
274
364
266
275
365
267
276
366
268
277
367
269
278
368
279
369
271
280
370
272
281
371
273
282
372
274
283
373
275
284
374
276
285
375
277
286
376
278
287
377
279
288
378
289
379
281
290
380
282
291
381
283
292
382
284
293
383
285
294
384
286
295
385
287
296
386
288
297
387
289
298
388
299
389
291
390
292
391
293
392
294
393
295
394
296
395
297
396
298
397
299
398
399
301
310
400
302
311
401
303
312
402
304
313
403
305
314
404
306
315
405
307
316
406
308
317
407
309
318
408
319
409
311
320
410
312
321
411
313
322
412
314
323
413
315
324
414
316
325
415
317
326
416
318
327
417
319
328
418
329
419
321
330
420
322
331
421
323
332
422
324
333
423
325
334
424
326
335
425
327
336
426
328
337
427
329
338
428
339
429
331
340
430
332
341
431
333
342
432
334
343
433
335
344
434
336
345
435
337
346
436
338
347
437
339
348
438
349
439
341
350
440
342
351
441
343
352
442
344
353
443
345
354
444
346
355
445
347
356
446
348
357
447
349
358
448
359
449
351
360
450
352
361
451
353
362
452
354
363
453
355
364
454
356
365
455
357
366
456
358
367
457
359
368
458
369
459
361
370
460
362
371
461
363
372
462
364
373
463
365
374
464
366
375
465
367
376
466
368
377
467
369
378
468
379
469
371
380
470
372
381
471
373
382
472
374
383
473
375
384
474
376
385
475
377
386
476
378
387
477
379
388
478
389
479
381
390
480
382
391
481
383
392
482
384
393
483
385
394
484
386
395
485
387
396
486
388
397
487
389
398
488
399
489
391
490
392
491
393
492
394
493
395
494
396
495
397
496
398
497
399
498
499
401
410
500
402
411
501
403
412
502
404
413
503
405
414
504
406
415
505
407
416
506
408
417
507
409
418
508
419
509
411
420
510
412
421
511
413
422
512
414
423
513
415
424
514
416
425
515
417
426
516
418
427
517
419
428
518
429
519
421
430
520
422
431
521
423
432
522
424
433
523
425
434
524
426
435
525
427
436
526
428
437
527
429
438
528
439
529
431
440
530
432
441
531
433
442
532
434
443
533
435
444
534
436
445
535
437
446
536
438
447
537
439
448
538
449
539
441
450
540
442
451
541
443
452
542
444
453
543
445
454
544
446
455
545
447
456
546
448
457
547
449
458
548
459
549
451
460
550
452
461
551
453
462
552
454
463
553
455
464
554
456
465
555
457
466
556
458
467
557
459
468
558
469
559
461
470
560
462
471
561
463
472
562
464
473
563
465
474
564
466
475
565
467
476
566
468
477
567
469
478
568
479
569
471
480
570
472
481
571
473
482
572
474
483
573
475
484
574
476
485
575
477
486
576
478
487
577
479
488
578
489
579
481
490
580
482
491
581
483
492
582
484
493
583
485
494
584
486
495
585
487
496
586
488
497
587
489
498
588
499
589
491
590
492
591
493
592
494
593
495
594
496
595
497
596
498
597
499
598
599
501
510
600
502
511
601
503
512
602
504
513
603
505
514
604
506
515
605
507
516
606
508
517
607
509
518
608
519
609
511
520
610
512
521
611
513
522
612
514
523
613
515
524
614
516
525
615
517
526
616
518
527
617
519
528
618
529
619
521
530
620
522
531
621
523
532
622
524
533
623
525
534
624
526
535
625
527
536
626
528
537
627
529
538
628
539
629
531
540
630
532
541
631
533
542
632
534
543
633
535
544
634
536
545
635
537
546
636
538
547
637
539
548
638
549
639
541
550
640
542
551
641
543
552
642
544
553
643
545
554
644
546
555
645
547
556
646
548
557
647
549
558
648
559
649
551
560
650
552
561
651
553
562
652
554
563
653
555
564
654
556
565
655
557
566
656
558
567
657
559
568
658
569
659
561
570
660
562
571
661
563
572
662
564
573
663
565
574
664
566
575
665
567
576
666
568
577
667
569
578
668
579
669
571
580
670
572
581
671
573
582
672
574
583
673
575
584
674
576
585
675
577
586
676
578
587
677
579
588
678
589
679
581
590
680
582
591
681
583
592
682
584
593
683
585
594
684
586
595
685
587
596
686
588
597
687
589
598
688
599
689
591
690
592
691
593
692
594
693
595
694
596
695
597
696
598
697
599
698
699
601
610
700
602
611
701
603
612
702
604
613
703
605
614
704
606
615
705
607
616
706
608
617
707
609
618
708
619
709
611
620
710
612
621
711
613
622
712
614
623
713
615
624
714
616
625
715
617
626
716
618
627
717
619
628
718
629
719
621
630
720
622
631
721
623
632
722
624
633
723
625
634
724
626
635
725
627
636
726
628
637
727
629
638
728
639
729
631
640
730
632
641
731
633
642
732
634
643
733
635
644
734
636
645
735
637
646
736
638
647
737
639
648
738
649
739
641
650
740
642
651
741
643
652
742
644
653
743
645
654
744
646
655
745
647
656
746
648
657
747
649
658
748
659
749
651
660
750
652
661
751
653
662
752
654
663
753
655
664
754
656
665
755
657
666
756
658
667
757
659
668
758
669
759
661
670
760
662
671
761
663
672
762
664
673
763
665
674
764
666
675
765
667
676
766
668
677
767
669
678
768
679
769
671
680
770
672
681
771
673
682
772
674
683
773
675
684
774
676
685
775
677
686
776
678
687
777
679
688
778
689
779
681
690
780
682
691
781
683
692
782
684
693
783
685
694
784
686
695
785
687
696
786
688
697
787
689
698
788
699
789
691
790
692
791
693
792
694
793
695
794
696
795
697
796
698
797
699
798
799
701
710
800
702
711
801
703
712
802
704
713
803
705
714
804
706
715
805
707
716
806
708
717
807
709
718
808
719
809
711
720
810
712
721
811
713
722
812
714
723
813
715
724
814
716
725
815
717
726
816
718
727
817
719
728
818
729
819
721
730
820
722
731
821
723
732
822
724
733
823
725
734
824
726
735
825
727
736
826
728
737
827
729
738
828
739
829
731
740
830
732
741
831
733
742
832
734
743
833
735
744
834
736
745
835
737
746
836
738
747
837
739
748
838
749
839
741
750
840
742
751
841
743
752
842
744
753
843
745
754
844
746
755
845
747
756
846
748
757
847
749
758
848
759
849
751
760
850
752
761
851
753
762
852
754
763
853
755
764
854
756
765
855
757
766
856
758
767
857
759
768
858
769
859
761
770
860
762
771
861
763
772
862
764
773
863
765
774
864
766
775
865
767
776
866
768
777
867
769
778
868
779
869
771
780
870
772
781
871
773
782
872
774
783
873
775
784
874
776
785
875
777
786
876
778
787
877
779
788
878
789
879
781
790
880
782
791
881
783
792
882
784
793
883
785
794
884
786
795
885
787
796
886
788
797
887
789
798
888
799
889
791
890
792
891
793
892
794
893
795
894
796
895
797
896
798
897
799
898
899
801
810
900
802
811
901
803
812
902
804
813
903
805
814
904
806
815
905
807
816
906
808
817
907
809
818
908
819
909
811
820
910
812
821
911
813
822
912
814
823
913
815
824
914
816
825
915
817
826
916
818
827
917
819
828
918
829
919
821
830
920
822
831
921
823
832
922
824
833
923
825
834
924
826
835
925
827
836
926
828
837
927
829
838
928
839
929
831
840
930
832
841
931
833
842
932
834
843
933
835
844
934
836
845
935
837
846
936
838
847
937
839
848
938
849
939
841
850
940
842
851
941
843
852
942
844
853
943
845
854
944
846
855
945
847
856
946
848
857
947
849
858
948
859
949
851
860
950
852
861
951
853
862
952
854
863
953
855
864
954
856
865
955
857
866
956
858
867
957
859
868
958
869
959
861
870
960
862
871
961
863
872
962
864
873
963
865
874
964
866
875
965
867
876
966
868
877
967
869
878
968
879
969
871
880
970
872
881
971
873
882
972
874
883
973
875
884
974
876
885
975
877
886
976
878
887
977
879
888
978
889
979
881
890
980
882
891
981
883
892
982
884
893
983
885
894
984
886
895
985
887
896
986
888
897
987
889
898
988
899
989
891
990
892
991
893
992
894
993
895
994
896
995
897
996
898
997
899
998
999
901
910
902
911
903
912
904
913
905
914
906
915
907
916
908
917
909
918
919
911
920
912
921
913
922
914
923
915
924
916
925
917
926
918
927
919
928
929
921
930
922
931
923
932
924
933
925
934
926
935
927
936
928
937
929
938
939
931
940
932
941
933
942
934
943
935
944
936
945
937
946
938
947
939
948
949
941
950
942
951
943
952
944
953
945
954
946
955
947
956
948
957
949
958
959
951
960
952
961
953
962
954
963
955
964
956
965
957
966
958
967
959
968
969
961
970
962
971
963
972
964
973
965
974
966
975
967
976
968
977
969
978
979
971
980
972
981
973
982
974
983
975
984
976
985
977
986
978
987
979
988
989
981
990
982
991
983
992
984
993
985
994
986
995
987
996
988
997
989
998
999
991
992
993
994
995
996
997
998
999
)


// ************************************************************************* //
