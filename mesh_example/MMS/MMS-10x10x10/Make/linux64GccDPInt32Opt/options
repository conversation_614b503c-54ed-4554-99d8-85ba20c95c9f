# 0 "Make/options"
# 0 "<built-in>"
# 0 "<command-line>"
# 8 "<command-line>"
# 1 "/usr/include/stdc-predef.h" 1 3 4

# 17 "/usr/include/stdc-predef.h" 3 4



















# 45 "/usr/include/stdc-predef.h" 3 4

# 55 "/usr/include/stdc-predef.h" 3 4









# 8 "<command-line>" 2
# 1 "Make/options"
EXE_INC = -I$(LIB_SRC)/finiteVolume/lnInclude -I$(LIB_SRC)/meshTools/lnInclude



EXE_LIBS = -lfiniteVolume -lmeshTools

# options
