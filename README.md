# TorchFVM
A pytorch-based differentiable Finite Volume Solver


# Catalog

- [TorchFVM](#torchfvm)
- [Catalog](#catalog)
- [Installation of the Code Environment](#installation-of-the-code-environment)
- [How To Use (Updated Workflow)](#how-to-use-updated-workflow)
  - [Step 1: Mesh Preparation (OpenFOAM)](#step-1-mesh-preparation-openfoam)
  - [Step 2: Pre-Training](#step-2-pre-training)
  - [Step 3: Inference](#step-3-inference)
    - [Inference Without Adam (Rollout)](#inference-without-adam-rollout)
    - [Inference With Adam (PINN-Style)](#inference-with-adam-pinn-style)
- [Code Directory and File Structure](#code-directory-and-file-structure)
    - [Instructions for Mesh Directories](#instructions-for-mesh-directories)
    - [Parameter Configuration](#parameter-configuration)
  - [Common Issues](#common-issues)
    - [Loss Calculation Produces NaN](#loss-calculation-produces-nan)

# Installation of the Code Environment

## PyTorch 2.7.0 + CuDSS Compilation Guide

This section provides a complete step-by-step guide to compile PyTorch 2.7.0 with CuDSS support from source.

### Prerequisites

- Ubuntu 22.04 (or compatible Linux distribution)
- CUDA 12.8
- GCC 12.3.0 or compatible
- CMake 3.22.1 or higher
- Python 3.11

### Step 1: Install CuDSS Library

Install CuDSS first on Ubuntu 22.04. If your system is different, please refer to NVIDIA's official site [[html]](https://developer.nvidia.com/cudss-downloads?target_os=Linux&target_arch=x86_64&Distribution=Ubuntu&target_version=22.04&target_type=deb_local).

```bash
wget https://developer.download.nvidia.com/compute/cudss/0.6.0/local_installers/cudss-local-repo-ubuntu2204-0.6.0_0.6.0-1_amd64.deb
sudo dpkg -i cudss-local-repo-ubuntu2204-0.6.0_0.6.0-1_amd64.deb
sudo cp /var/cudss-local-repo-ubuntu2204-0.6.0/cudss-*-keyring.gpg /usr/share/keyrings/
sudo apt-get update
sudo apt-get -y install cudss
```

### Step 2: Create Conda Environment

```bash
# Create a new conda environment with Python 3.11
conda create -n FVGN-pt2.7-sp python=3.11 -y
conda activate FVGN-pt2.7-sp
```

### Step 3: Download PyTorch Source Code

```bash
# Clone PyTorch repository
cd /path/to/your/workspace  # Change to your preferred directory
git clone --recursive https://github.com/pytorch/pytorch
cd pytorch

# Checkout to PyTorch 2.7.0 release
git checkout v2.7.0
git submodule sync
git submodule update --init --recursive
```

### Step 4: Install Compilation Dependencies

```bash
# Install build dependencies via conda
conda install cmake ninja -y

# Install Python dependencies
pip install -r requirements.txt
pip install mkl-static mkl-include
```

### Step 5: Compile PyTorch with CuDSS Support

```bash
# Set environment variables for compilation
export CMAKE_PREFIX_PATH="${CONDA_PREFIX:-'$(dirname $(which conda))/../'}:${CMAKE_PREFIX_PATH}"
export USE_CUDSS=1
export MAX_JOBS=24  # Use half of your CPU cores (adjust based on your system)

# Compile and install PyTorch
python setup.py develop
```

**Note**: The compilation process may take 1-2 hours depending on your system. The `MAX_JOBS=24` setting uses 24 CPU cores; adjust this to half of your available cores for optimal performance.

### Step 6: Verify Installation

```bash
# Test PyTorch installation
python -c "
import torch
print('PyTorch version:', torch.__version__)
print('CUDA available:', torch.cuda.is_available())
print('CUDA version:', torch.version.cuda)
print('Number of GPUs:', torch.cuda.device_count())

# Test torch.sparse.spsolve with CuDSS
device = 'cuda' if torch.cuda.is_available() else 'cpu'
row_indices = torch.tensor([0, 0, 1, 1, 2, 2], dtype=torch.long)
col_indices = torch.tensor([0, 1, 1, 2, 0, 2], dtype=torch.long)
values = torch.tensor([2.0, 1.0, 3.0, 1.0, 1.0, 4.0], dtype=torch.float32)
indices = torch.stack([row_indices, col_indices])
sparse_coo = torch.sparse_coo_tensor(indices, values, (3, 3), dtype=torch.float32, device=device)
sparse_csr = sparse_coo.to_sparse_csr()
b = torch.tensor([1.0, 2.0, 3.0], dtype=torch.float32, device=device)

try:
    x = torch.sparse.spsolve(sparse_csr, b)
    print('✅ torch.sparse.spsolve with CuDSS works!')
    print('Solution:', x)
except Exception as e:
    print('❌ Error:', e)
"
```

### Step 7: Install Additional Dependencies

```bash
# Install PyTorch Geometric and related packages
pip install torch_geometric -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install scipy
pip install --no-index pyg_lib torch_scatter torch_sparse torch_cluster torch_spline_conv -f https://pytorch-geometric.com/whl/torch-2.7.0+cu128.html

# Install project requirements (excluding timm to avoid PyTorch reinstallation)
pip install -r src/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# Install additional dependencies without reinstalling PyTorch
pip install torchtyping --no-deps
```

**Important Notes**:

- The compiled PyTorch version will be `2.7.0a0+git1341794` (development version of 2.7.0)
- CuDSS support enables high-performance sparse linear system solving via `torch.sparse.spsolve`
- We avoid installing `timm` as it would try to reinstall PyTorch from PyPI, overwriting your custom compilation
- The code has been modified to use PyTorch's built-in `torch.nn.init.trunc_normal_` instead of `timm.layers.trunc_normal_`


# How To Use (Updated Workflow)

Gen-FVGN uses a custom `.h5` data format. The workflow begins by converting a standard OpenFOAM case into this format.

## Step 1: Mesh Preparation (OpenFOAM)

The model now exclusively supports **3D unstructured meshes** from **OpenFOAM**.

1.  **Prepare an OpenFOAM case directory.** A sample case, `lidDrivenCavity3D-simple`, is provided in the `mesh_example/` directory. Your case directory must contain the standard OpenFOAM structure, including `constant/polyMesh` and the initial condition files in the `0/` directory (e.g., `U`, `p`).

2.  **Convert the mesh to `.h5` format.** Open the `src/Extract_mesh/parse_openfoam.py` script and set the `case_path` variable to point to your OpenFOAM case directory.

    ```python
    # In src/Extract_mesh/parse_openfoam.py
    case_path = '../../mesh_example/lidDrivenCavity3D-simple' 
    ```

3.  **Run the conversion script:**
    ```bash
    python src/Extract_mesh/parse_openfoam.py
    ```
    This will process the OpenFOAM mesh and boundary conditions, generating a single `data.h5` file inside your case directory. This `.h5` file contains all the necessary geometric and boundary information for the model.

## Step 2: Pre-Training
This step trains the GNN model to solve the PDE defined by your mesh and parameters.

1.  **Configure parameters.** Open `src/pre_train_Adam.py` and `src/Utils/get_param.py` to adjust training parameters. The most important parameter is `--dataset_dir`, which must point to the directory containing your `data.h5` file.

    ```python
    # In src/pre_train_Adam.py or src/Utils/get_param.py
    params.dataset_dir = "mesh_example/lidDrivenCavity3D-simple"
    ```
    You can also adjust other parameters like learning rate (`--lr`), batch size (`--batch_size`), and the number of epochs (`--n_epochs`).

2.  **Start training:**
    ```bash
    python src/pre_train_Adam.py
    ```
    The training progress, logs, and model checkpoints will be saved in the `Logger/` directory.

## Step 3: Inference
After training, you can use the saved model to perform inference and solve for the flow field.

### Inference Without Adam (Rollout)
This method uses the trained model directly for rapid prediction.

1.  **Configure the inference script.** Open `src/solve_without_grad_GPU.py` and set the following parameters:
    *   `params.load_date_time`: The timestamp of the trained model you want to use (e.g., `"2025-06-14-..."`). This corresponds to a folder in the `Logger/` directory.
    *   `params.load_index`: The model checkpoint index to load (e.g., `1`).
    *   `params.dataset_dir`: The path to the mesh directory for inference.

2.  **Run inference:**
    ```bash
    python src/solve_without_grad_GPU.py
    ```

### Inference With Adam (PINN-Style)
This method fine-tunes the model during inference, which can improve accuracy but is slower.

1.  **Configure the inference script.** Open `src/solve_with_grad_GPU.py`. Set the `load_date_time`, `load_index`, and `dataset_dir` parameters as in the previous method. You must also set `params.max_inner_steps` to define the number of optimization steps.

2.  **Run inference:**
    ```bash
    python src/solve_with_grad_GPU.py
    ```
The results of the inference will be saved as Tecplot-compatible files in the mesh directory.

# Code Directory and File Structure

- **datasets/**: A directory to store your mesh files. This path is included in `.gitignore`, so local files won't be tracked by git. You may need to create this folder.
- **mesh_example/**: Contains an example OpenFOAM case (`lidDrivenCavity3D-simple`) to demonstrate the expected structure.
- **Logger/**: Records files generated during training, including model checkpoints, logs, and solution files.
- **src/**: The core source code directory, containing model implementations, training/inference scripts, and mesh processing tools.

### Instructions for Mesh Directories
The path to the mesh directory is a critical parameter. It must be set correctly in the training and inference scripts via the `--dataset_dir` argument.

An example OpenFOAM case is located at `mesh_example/lidDrivenCavity3D-simple`. After running the `src/Extract_mesh/parse_openfoam.py` script, the directory will contain the generated `data.h5` file alongside the original OpenFOAM files:
```
--- lidDrivenCavity3D-simple/
    |-- 0/
    |   |-- p
    |   `-- U
    |-- constant/
    |   `-- polyMesh/
    |       |-- boundary
    |       |-- faces
    |       |-- neighbour
    |       |-- owner
    |       `-- points
    |-- system/
    |   |-- blockMeshDict
    |   |-- controlDict
    |   |-- fvSchemes
    |   `-- fvSolution
    `-- h5  <-- Generated by the script
    `-- log  <-- Generated by the script
```
