import sys
import os
import warnings

# 在导入torch之前就设置警告过滤器
warnings.filterwarnings("ignore", message=".*Sparse CSR tensor support is in beta state.*")

cur_path = os.path.split(__file__)[0]
sys.path.append(cur_path)

import torch
import numpy as np

# import os
from FVdomain import Graph_loader
from Utils import get_param
import time
from Utils.get_param import get_hyperparam
from Utils.Logger import Logger
from Post_process.to_vtk import export_full_mesh_vtu
import random
import datetime
from FVmodel.FVdiscretization.FVgrad import gradient_reconstruction,compute_normal_matrix,green_gauss_gradient
from FVmodel.FVdiscretization.FVInterpolation import FV_Interpolation
from Utils.utilities import NodeType
from torch_scatter import scatter

def assemble_matrix_sparse(owner_i, neighbor_i, a_C, a_F, num_cells, device):
    """
    使用稀疏矩阵的高效矩阵组装方法 - 节省内存
    修正：与assemble_matrix_efficient_v2保持一致，只添加owner->neighbor连接

    参数:
    - owner_i: 内部面的拥有者单元索引 [N_internal_faces]
    - neighbor_i: 内部面的邻居单元索引 [N_internal_faces]
    - a_C: 主对角线系数 [num_cells, 1]
    - a_F: 非主对角线系数 [N_internal_faces, 1]
    - num_cells: 单元总数
    - device: 设备

    返回:
    - A_sparse: 组装好的稀疏系数矩阵
    """
    # 准备稀疏矩阵的索引和值
    indices_list = []
    values_list = []

    # 主对角线
    diag_indices = torch.arange(num_cells, device=device)
    indices_list.append(torch.stack([diag_indices, diag_indices]))
    values_list.append(a_C.squeeze())

    # 非主对角线 - 只添加 owner -> neighbor（与efficient_v2一致）
    indices_list.append(torch.stack([owner_i, neighbor_i]))
    values_list.append(a_F.squeeze())

    # 合并所有索引和值
    all_indices = torch.cat(indices_list, dim=1)
    all_values = torch.cat(values_list, dim=0)

    # 创建稀疏矩阵
    A_sparse = torch.sparse_coo_tensor(
        all_indices, all_values,
        (num_cells, num_cells),
        device=device
    ).coalesce()

    return A_sparse

def assemble_matrix_efficient_v2(owner_i, neighbor_i, a_C, a_F, num_cells, device):
    """
    更高效的矩阵组装方法 - 使用高级索引

    参数:
    - owner_i: 内部面的拥有者单元索引 [N_internal_faces]
    - neighbor_i: 内部面的邻居单元索引 [N_internal_faces]
    - a_C: 主对角线系数 [num_cells, 1]
    - a_F: 非主对角线系数 [N_internal_faces, 1]
    - num_cells: 单元总数
    - device: 设备

    返回:
    - A: 组装好的系数矩阵 [num_cells, num_cells]
    """
    # 初始化矩阵
    A = torch.zeros((num_cells, num_cells), device=device)

    # 设置主对角线
    diag_indices = torch.arange(num_cells, device=device)
    A[diag_indices, diag_indices] = a_C.squeeze()

    # 设置非主对角线 - 使用高级索引，比循环快得多
    A[owner_i, neighbor_i] = a_F.squeeze()

    return A



def solve_sparse_system(A_sparse, b, max_iter=1000, tol=1e-6):
    """
    使用PyTorch的稀疏求解器求解线性系统 Ax = b
    注意：torch.sparse.spsolve只在CUDA设备上可用

    参数:
    - A_sparse: 稀疏系数矩阵
    - b: 右端项
    - max_iter: 最大迭代次数（未使用，保留接口兼容性）
    - tol: 收敛容差（未使用，保留接口兼容性）

    返回:
    - x: 解向量
    - converged: 是否收敛
    - iterations: 实际迭代次数
    """
    try:
        # 检查设备，torch.sparse.spsolve只在CUDA上可用
        if not A_sparse.is_cuda:
            raise RuntimeError("torch.sparse.spsolve只在CUDA设备上可用，请将矩阵和向量移动到GPU")

        # 确保矩阵是CSR格式，这是torch.sparse.spsolve要求的格式
        if A_sparse.layout == torch.sparse_coo:
            A_csr = A_sparse.to_sparse_csr()
        elif A_sparse.layout == torch.sparse_csr:
            A_csr = A_sparse
        else:
            # 如果是其他格式，先转换为COO再转换为CSR
            A_csr = A_sparse.coalesce().to_sparse_csr()

        x = torch.sparse.spsolve(A_csr, b)
        return x, True, 0  # 0表示未使用最大迭代次数
    except RuntimeError as e:
        print(f"稀疏线性求解器错误: {e}")
        print("提示：torch.sparse.spsolve只在CUDA设备上可用")
        return None, False, 0
  

# configurate parameters
params = get_param.params()
seed = int(datetime.datetime.now().timestamp())
np.random.seed(seed)
random.seed(seed)
torch.manual_seed(seed)
torch.cuda.set_per_process_memory_fraction(0.8, params.on_gpu)

device = "cuda"
params.dataset_dir="mesh_example/lidDrivenCavity3D-simple"
params.dataset_size=1
params.batch_size=1
params.order = "2nd" # 1st, 2nd, 3rd, 4th, GG

# initialize Logger and load model / optimizer if according parameters were given
logger = Logger(
    get_hyperparam(params),
    use_csv=True,
    use_tensorboard=False,
    params=params,
    copy_code=True,
    seed=seed,
)

# initialize Training Dataset
start = time.time()
datasets_factory = Graph_loader.DatasetFactory(
    params=params,
    dataset_dir=params.dataset_dir,
    state_save_dir=logger.saving_path,
    device=device,
)

# refresh dataset size
params.dataset_size = datasets_factory.dataset_size

# create dataset objetc
datasets, loader = datasets_factory.create_loader(
    batch_size=params.batch_size, num_workers=0, pin_memory=False, shuffle=False
)

end = time.time()
print("Training traj has been loaded time consuming:{0}".format(end - start))

''' >>> fetch data and move to GPU >>> '''
fv_graph = next(iter(loader))
fv_graph = fv_graph.to(device, exclude_keys=['global_idx'])
''' <<< fetch data and move to GPU <<< '''

''' FV Graph Data Structure Description 

The fv_graph is a comprehensive finite volume graph structure containing multiple sub-graphs:

=== fv_graph.graph_node ===
Node-level data structure representing mesh vertices:
    - face_node: [num_faces*max_nodes_per_face] - Face-to-node connectivity (node indices flattened)
    - cells_node: [num_cells*max_nodes_per_cell] - Cell-to-node connectivity (node indices flattened)
    - num_nodes: int - Total number of mesh nodes
    - _num_nodes: int - Internal node count reference
    - graph_index: [1] - Graph batch index

=== fv_graph.graph_face ===  
Face-level data structure representing mesh faces/edges:
    - face_type: [num_faces] - Face type classification (boundary, interior, etc. refer to src/Utils/utilities.py)
    - face_area: [num_faces] - Area of each face
    - cells_face: [num_cells*max_faces_per_cell] - Cell-to-face connectivity
    - face_node_ptr: [num_faces*max_nodes_per_face] - same length as cells_face since face_node is flattened, this array is used for distinguish node idx in face_node belong to which face
    - pos: [num_faces, D_pos] - Face center positions
    - _num_faces: int - Total number of faces
    - graph_index: [1] - Graph batch index

=== fv_graph.graph_cell ===
Cell-level data structure representing finite volume cells:
    - x: [num_cpd_cells, 4] - Physical field variables (u,v,w,p) at cell centers
    - neighbor_cell: [2, num_cell_to_cell edges] - Cell-to-cell connectivity (cpd_neighbor_cell)
    - cells_face_unv: [num_cells*max_faces_per_cell, D_norm] - flattened Unit normal vectors of cell faces
    - cells_volume: [num_cells, 1] - Volume of each cell
    - cell_type: [num_cells, 1] - Cell type (NORMAL=0, BOUNDARY=1, etc.)
    - cpd_centroid: [num_cpd_cells, D_pos] - Compound cell centroid positions, cell concated with boundary faces
    - y: [num_cpd_cells, C_target] - Target values for training/validation
    - global_idx: [num_cpd_cells] - Global indexing for cells
    - cells_node_ptr: [num_cells*max_nodes_per_cell] - same length as cells_face since face_node is flattened, this array is used for distinguish node idx in face_node belong to which face
    - cells_face_ptr: [num_cells*max_faces_per_cell] - same length as cells_face since face_node is flattened, this array is used for distinguish face idx in face_node belong to which face
    - case_name: [L_case_name] - Case identifier (encoded as character ordinals)
    - num_nodes: int - Number of compound cells (= _num_cpd_cells)
    - _num_cells: int - Total number of standard cells
    - _num_cpd_cells: int - Total number of compound cells
    - graph_index: [1] - Graph batch index

Note: 
- "cpd" refers to "compound" cells, which represent merged interior cells with boundary faces
- The graph structure supports multi-level connectivity (node, face, cell) for FVM operations
- Cell-centered approach is used where physical variables are stored at cell centroids

'''
cells_face = fv_graph.graph_face.cells_face
face_type = fv_graph.graph_face.face_type
neighbor_cell = fv_graph.graph_cell.neighbor_cell

''' >>> 处理内部域 >>> '''
internal_neighbor_cell_mask = ~(neighbor_cell[0] == neighbor_cell[1])
internal_face_mask = (face_type==NodeType.NORMAL)
if (~(internal_face_mask==internal_neighbor_cell_mask)).sum() > 0:
    raise ValueError("internal_face_mask != internal_neighbor_cell_mask, 说明neighbor cell生成有错, neighbor cell应该与'face|'开头的变量维度保持一致")

# 在cells_face中筛选出internal face
internal_cells_face_mask = internal_face_mask[cells_face]
internal_cells_face = cells_face[internal_cells_face_mask]

owner = fv_graph.graph_cell.cells_face_ptr
owner_i = fv_graph.graph_cell.cells_face_ptr[internal_cells_face_mask]

# 筛出内部的与owner对应的neighbor
senders_cell, receivers_cell = fv_graph.graph_cell.neighbor_cell[:,internal_cells_face]

neighbor_i = torch.where(senders_cell==owner_i, receivers_cell, senders_cell)

# 开始计算非正交修正几何扩散系数
centroid = fv_graph.graph_cell.centroid
face_center_pos = fv_graph.graph_face.pos

# 几何系数
E_CF_i = centroid[neighbor_i] - centroid[owner_i]
d_CF_i = torch.norm(E_CF_i, dim=1, keepdim=True)
e_CF_i = E_CF_i / d_CF_i

face_area_i = fv_graph.graph_face.face_area[internal_cells_face]
cells_face_surf_vec_i = fv_graph.graph_cell.cells_face_unv[internal_cells_face_mask] * face_area_i[:, None]

Ef_vec_i = ((cells_face_surf_vec_i[:,None,:]@cells_face_surf_vec_i[:,:,None])/(cells_face_surf_vec_i[:,None,:]@e_CF_i[:,:,None]))*e_CF_i[:,None,:] # page 243
Ef_sca_i = torch.norm(Ef_vec_i, dim=-1, keepdim=True)
Tf_vec_i = (cells_face_surf_vec_i[:,None,:]-Ef_vec_i).transpose(1,2) # [N_cells_face_internal, 1, 3] Over-relaxation term page 243

gDiff_f = Ef_sca_i/d_CF_i[:,:,None] # [N_cells_face_internal, 1, 1]
''' <<< 处理内部域 <<< '''


''' >>> 边界域 >>> '''
boundary_neighbor_cell_mask = (neighbor_cell[0] == neighbor_cell[1])
boundary_face_mask = ~(face_type==NodeType.NORMAL)
if (~(boundary_face_mask==boundary_neighbor_cell_mask)).sum() > 0:
    raise ValueError("boundary_face_mask != boundary_neighbor_cell_mask, 说明neighbor cell生成有错, neighbor cell应该与'face|'开头的变量维度保持一致")

# 在cells_face中筛选出boundary face
boundary_cells_face_mask = boundary_face_mask[cells_face]
boundary_cells_face = cells_face[boundary_cells_face_mask]

owner_b = fv_graph.graph_cell.cells_face_ptr[boundary_cells_face_mask]
neighbor_b = cells_face[boundary_cells_face_mask] # 边界是面，因此选出cells_face中属于边界的face idx

# 开始计算非正交修正几何扩散系数
centroid = fv_graph.graph_cell.centroid
cpd_centroid = fv_graph.graph_cell.cpd_centroid
face_center_pos = fv_graph.graph_face.pos

# 几何系数
E_CF_b = face_center_pos[neighbor_b] - centroid[owner_b]
d_CF_b = torch.norm(E_CF_b, dim=1, keepdim=True)
e_CF_b = E_CF_b / d_CF_b

face_area_b = fv_graph.graph_face.face_area[boundary_cells_face]
cells_face_surf_vec_b = fv_graph.graph_cell.cells_face_unv[boundary_cells_face_mask] * face_area_b[:,None]

Ef_vec_b = ((cells_face_surf_vec_b[:,None,:]@cells_face_surf_vec_b[:,:,None])/(cells_face_surf_vec_b[:,None,:]@e_CF_b[:,:,None]))*e_CF_b[:,None,:] # page 243
Ef_sca_b = torch.norm(Ef_vec_b, dim=-1, keepdim=True)
Tf_vec_b = (cells_face_surf_vec_b[:,None,:]-Ef_vec_b).transpose(1,2) # Over-relaxation term page 243

gDiff_b = Ef_sca_b/d_CF_b[:,:,None]
''' <<< 处理内部域 <<< '''


# 初始化系数矩阵
num_cells = fv_graph.graph_cell._num_cells
num_cpd_cells = fv_graph.graph_cell._num_cpd_cells
cpd_cell_value = torch.ones((num_cpd_cells,1),device=device) # 初始化，用于延迟计算梯度

B = torch.zeros((num_cells,1) ,device=device)

# 为施加边界条件做准备
cpd_cell_type = fv_graph.graph_cell.cpd_cell_type
internal_cell = (cpd_cell_type==NodeType.NORMAL).squeeze()
boundary_cellf = ~internal_cell
inflow_mask = (cpd_cell_type==NodeType.INFLOW).squeeze()
wall_mask = (cpd_cell_type==NodeType.WALL_BOUNDARY).squeeze()


# 泊松方程系数配置
diff_coeff = 0.01
cells_volume = fv_graph.graph_cell.cells_volume.view(-1,1)
Qv = torch.full((cells_volume.shape[0],1), 1, device=device)
# Qv = (torch.sin(centroid)**2 + torch.sin(cells_volume)*torch.cos(cells_volume) + torch.cos(cells_volume)**2).norm(dim=1, keepdim=True)

Flux_C = torch.zeros((cells_face.shape[0],gDiff_b.shape[1]),device = device)
Flux_V = torch.zeros((cells_face.shape[0],gDiff_b.shape[1]),device = device)

# 初始化插值器
FV_interp = FV_Interpolation()
FV_interp.register_geometrics(fv_graph.graph_cell, fv_graph.graph_face, fv_graph.graph_index)
num_faces = fv_graph.graph_face._num_faces


num_channel = 1 # 泊松方程是标量方程
grad_phi_face = torch.full((num_faces, num_channel, 3), 0., device=device)
phi_face  = torch.full((num_faces, num_channel), 0., device=device)

# 收敛控制参数
max_epochs = 100
tolerance = 1e-7  # 残差收敛容差
print_frequency = 10  # 每隔多少步打印一次残差 (可以设为1来每步都打印)
use_relative_residual = True  # 是否使用相对残差作为收敛判据
monitor_iteration_time = False  # 是否监控每次迭代的耗时

print(f"开始泊松方程求解")
print(f"最大迭代次数: {max_epochs}")
print(f"收敛容差: {tolerance:.2e}")
print(f"收敛判据: {'相对残差' if use_relative_residual else '绝对残差'}")
print(f"网格规模: {num_cells} 个单元")
print("=" * 80)
if monitor_iteration_time:
    print(f"{'Iter':>4s} {'Eq_Residual':>12s} {'Sol_Change':>12s} {'Rel_Residual':>12s} {'Time(s)':>8s} {'Status':>10s}")
    print("-" * 88)
else:
    print(f"{'Iter':>4s} {'Eq_Residual':>12s} {'Sol_Change':>12s} {'Rel_Residual':>12s} {'Status':>10s}")
    print("-" * 80)

# 残差历史记录 (可选)
residual_history = []

# 开始计时
solve_start_time = time.time()

for i in range(max_epochs):

    # 单次迭代计时开始
    if monitor_iteration_time:
        iter_start_time = time.time()

    # 施加边界条件
    cpd_cell_value[inflow_mask] = 2*torch.sin(2*torch.pi*cpd_centroid[inflow_mask,0:1]) # 上表面狄利克雷边界条件设置为1
    cpd_cell_value[wall_mask] = 0. # 墙壁狄利克雷边界条件设置为0
    
    # 首先计算当前迭代步下全场的梯度，用于后续非正交修正
    grad_phi = gradient_reconstruction(
        order=params.order, 
        phi_node=cpd_cell_value, 
        edge_index=fv_graph.graph_cell_x.neighbor_cell_x, 
        mesh_pos=fv_graph.graph_cell.cpd_centroid, 
    )[:,:,0:3] # [N_cpd_cells, 1, 3], 仅取出梯度，即一阶导数即可
    
    # 开始插值梯度和phi到面上去
    grad_phi_face[FV_interp.mask_interior_face,0,0:3] = FV_interp.interpolating_gradients_to_faces(
        phi_cell=cpd_cell_value, # 这里的输入就是要cpd的版本,在插值器内部自动替换为内部的neighbor_cell
        grad_phi_cell=grad_phi, # 这里的输入就是要cpd的版本,在插值器内部自动替换为内部的neighbor_cell
    )
    grad_phi_face[FV_interp.mask_boundary_face] = grad_phi[FV_interp.mask_boundary_cell] # 边界处梯度是直接计算的

    phi_face[FV_interp.mask_interior_face] = FV_interp.interpolating_phic_to_faces(
        phi_cell=cpd_cell_value, # [Cell，Nc] Cell是单元数, Nc是通道数
        grad_phi_cell=grad_phi, # [Cell，Nc，3] Cell是单元数, Nc是通道数, 3为3维情况下梯度分量
    ).view(-1,num_channel)
    phi_face[FV_interp.mask_boundary_face] = cpd_cell_value[FV_interp.mask_boundary_cell] # 边界值保持为边界条件不变
    
    ''' >>> 组装系数矩阵 >>> '''
    a_F = -diff_coeff*gDiff_f[:,0] # 非主对角线系数
    
    Flux_Cf = diff_coeff*gDiff_f[:,0]
    Flux_C[internal_cells_face_mask] = Flux_Cf
    Flux_Cb = diff_coeff*gDiff_b[:,0]
    Flux_C[boundary_cells_face_mask] = Flux_Cb
    
    a_C = scatter(
        src = Flux_C, 
        index = owner, 
        dim=0, 
        dim_size=num_cells,
        reduce='sum',
    ) # 主对角线系数
    
    Flux_V[internal_cells_face_mask] = (diff_coeff*grad_phi_face[internal_cells_face] @ Tf_vec_i).squeeze(1)
    Flux_V[boundary_cells_face_mask] = (
        diff_coeff*gDiff_b[:,0]*phi_face[boundary_cells_face] +\
        ((diff_coeff*grad_phi_face[boundary_cells_face]) @ Tf_vec_b)[:,0]
    )
    
    b_C = cells_volume * Qv +\
        scatter(
            src = Flux_V,
            index = owner, 
            dim=0, 
            dim_size=num_cells,
            reduce='sum',
        ) # 右侧项 RHS
        
    A_sparse = assemble_matrix_sparse(owner_i, neighbor_i, a_C, a_F, num_cells, device)
    # A_dense = assemble_matrix_efficient_v2(owner_i, neighbor_i, a_C, a_F, num_cells, device)
    ''' <<< 组装系数矩阵 <<< '''
    
    # 6. 求解线性系统
    B = b_C

    # 保存上一步的解用于计算残差
    if i == 0:
        cpd_cell_value_old = cpd_cell_value.clone()
    else:
        cpd_cell_value_old = cpd_cell_value.clone()

    # 求解线性方程组
    # solution = torch.linalg.solve(A_dense, B)
    
    # 使用稀疏求解器求解线性方程组
    solution, converged, iterations = solve_sparse_system(A_sparse, B.squeeze(), max_iter=3000, tol=1e-10)
    if len(solution.size())<2:
        solution = solution.unsqueeze(1)
    if not converged:
        print(f"  警告: 线性求解器在第{i+1}次迭代中未收敛，迭代次数: {iterations}")
        
    cpd_cell_value[internal_cell] = solution

    # 计算残差
    # 方法1: 计算方程残差 ||Ax - b||
    residual_equation = torch.norm(A_sparse @ solution - B).item()

    # 方法2: 计算解的变化 ||x_new - x_old||
    residual_solution = torch.norm(cpd_cell_value[internal_cell] - cpd_cell_value_old[internal_cell]).item()

    # 方法3: 计算相对残差
    solution_norm = torch.norm(cpd_cell_value[internal_cell]).item()
    relative_residual = residual_solution / (solution_norm + 1e-12)  # 避免除零

    # 单次迭代计时结束
    if monitor_iteration_time:
        iter_end_time = time.time()
        iter_time = iter_end_time - iter_start_time
    else:
        iter_time = 0.0

    # 记录残差历史
    residual_history.append({
        'iteration': i + 1,
        'equation_residual': residual_equation,
        'solution_change': residual_solution,
        'relative_residual': relative_residual,
        'iteration_time': iter_time
    })

    # 选择收敛判据
    convergence_residual = relative_residual if use_relative_residual else residual_solution

    # 判断收敛状态
    if i == 0:
        status = "Initial"
    elif convergence_residual < tolerance:
        status = "Converged"
    elif convergence_residual < tolerance * 10:
        status = "Near Conv"
    else:
        status = "Iterating"

    # 打印残差信息
    if i % print_frequency == 0 or i == max_epochs - 1 or status == "Converged":
        if monitor_iteration_time:
            print(f"{i+1:4d} {residual_equation:12.6e} {residual_solution:12.6e} {relative_residual:12.6e} {iter_time:8.4f} {status:>10s}")
        else:
            print(f"{i+1:4d} {residual_equation:12.6e} {residual_solution:12.6e} {relative_residual:12.6e} {status:>10s}")

    # 收敛检查
    if convergence_residual < tolerance and i > 0:
        print("-" * 80)
        print(f"✓ 收敛达到! 在第 {i+1} 次迭代")
        print(f"最终{'相对' if use_relative_residual else '绝对'}残差: {convergence_residual:.6e} < {tolerance:.6e}")
        print("=" * 80)
        break
else:
    # 如果循环正常结束（没有break），说明未收敛
    print(f"\n警告: 在 {max_epochs} 次迭代后未收敛!")
    print(f"最终相对残差: {relative_residual:.6e} >= {tolerance:.6e}")
    print("建议: 增加最大迭代次数或放宽收敛容差")
    print("=" * 80)

# 计算总耗时
solve_end_time = time.time()
total_solve_time = solve_end_time - solve_start_time

print(f"\n求解完成，最终解的范围: [{cpd_cell_value[internal_cell].min().item():.6f}, {cpd_cell_value[internal_cell].max().item():.6f}]")
print(f"总求解耗时: {total_solve_time:.3f} 秒")
print(f"平均每次迭代耗时: {total_solve_time / len(residual_history):.4f} 秒")

# 打印收敛历史总结
if len(residual_history) > 1:
    initial_residual = residual_history[0]['relative_residual']
    final_residual = residual_history[-1]['relative_residual']
    reduction_factor = initial_residual / (final_residual + 1e-12)
    print(f"残差降低倍数: {reduction_factor:.2e} (从 {initial_residual:.2e} 到 {final_residual:.2e})")

# 性能统计
print("\n性能统计:")
print("-" * 40)
print(f"实际迭代次数: {len(residual_history)}")
print(f"网格规模: {num_cells} 个单元")
print(f"矩阵非零元素数: {len(owner_i) * 2 + num_cells}")  # 估算非零元素数
print(f"求解效率: {num_cells / total_solve_time:.0f} 单元/秒")

if monitor_iteration_time and len(residual_history) > 0:
    iteration_times = [r['iteration_time'] for r in residual_history if r['iteration_time'] > 0]
    if iteration_times:
        avg_iter_time = sum(iteration_times) / len(iteration_times)
        min_iter_time = min(iteration_times)
        max_iter_time = max(iteration_times)
        print(f"平均迭代时间: {avg_iter_time:.4f} 秒")
        print(f"最快迭代时间: {min_iter_time:.4f} 秒")
        print(f"最慢迭代时间: {max_iter_time:.4f} 秒")

# 可选：保存残差历史到文件
# import json
# with open(f"{father_dir}/residual_history.json", 'w') as f:
#     json.dump(residual_history, f, indent=2)

''' 将梯度保存到vtu文件 '''
cpd_cell_type = fv_graph.graph_cell.cpd_cell_type
interior_cell_mask = (cpd_cell_type== NodeType.NORMAL)
father_dir = os.path.dirname(logger.saving_path)
for _ in range(1):
    father_dir = os.path.dirname(father_dir)
case_name = "".join(
    chr(code) for code in fv_graph.graph_cell.case_name.cpu().tolist()
)
os.makedirs(f"{father_dir}/Grad_test", exist_ok=True)
# Prepare VTU output data for 3D meshes
vtu_data = {
    "cell|Qv": Qv.cpu().numpy(),
    "cell|cpd_cell_value": cpd_cell_value[interior_cell_mask].cpu().numpy(),
}
export_full_mesh_vtu(
    mesh_pos=datasets.meta_pool[fv_graph.graph_cell.graph_index.cpu().item()]["node|pos"], 
    pv_cells_node=datasets.meta_pool[fv_graph.graph_cell.graph_index.cpu().item()]["pv_cells_node"], 
    pv_cells_type=datasets.meta_pool[fv_graph.graph_cell.graph_index.cpu().item()]["pv_cells_type"],
    save_file_path=f"{father_dir}/Grad_test/grad_{case_name}_{params.order}.vtu",
    data_dict=vtu_data
)
''' 将梯度保存到vtu文件 '''
