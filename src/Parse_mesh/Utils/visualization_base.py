"""
Base visualization classes for mesh data.

Provides common visualization functionality for different mesh formats.
"""

import os
import logging
import numpy as np
import torch
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod


class BaseVisualization(ABC):
    """Base class for mesh visualization"""
    
    def __init__(self, mesh_manager):
        """
        Initialize visualization with mesh manager.
        
        Args:
            mesh_manager: Mesh manager instance containing mesh data
        """
        self.mesh_manager = mesh_manager
        
    @abstractmethod
    def create_visualization(self, output_dir: str, case_name: str, **kwargs):
        """Create visualization output"""
        pass


class MatplotlibVisualization(BaseVisualization):
    """Matplotlib-based 3D visualization"""
    
    def create_visualization(self, output_dir: str, case_name: str, **kwargs):
        """Create 3D matplotlib visualization"""
        try:
            import matplotlib.pyplot as plt
            from mpl_toolkits.mplot3d import Axes3D
            
            logging.info("Creating 3D matplotlib visualization...")
            
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            save_path = os.path.join(output_dir, f"cell_graph_3D_{case_name}.png")
            
            # Prepare data
            cpd_centroid_np = self.mesh_manager.cpd_centroid.detach().cpu().numpy()
            cpd_neighbor_cell_np = self.mesh_manager.cpd_neighbor_cell.detach().cpu().numpy()
            
            # Filter out self-loops for cleaner visualization
            valid_edge_mask = cpd_neighbor_cell_np[0] != cpd_neighbor_cell_np[1]
            valid_edges = cpd_neighbor_cell_np[:, valid_edge_mask]
            
            # Create 3D plot
            fig = plt.figure(figsize=(16, 12))
            ax = fig.add_subplot(111, projection='3d')
            
            # Configure plot appearance
            self._configure_plot_appearance(ax)
            
            # Plot edges
            self._plot_edges(ax, cpd_centroid_np, valid_edges)
            
            # Plot points
            self._plot_points(ax, cpd_centroid_np)
            
            # Set labels and title
            ax.set_xlabel('X')
            ax.set_ylabel('Y')
            ax.set_zlabel('Z')
            ax.set_title(f'3D Mesh Structure: {case_name}')
            
            # Set proper aspect ratio
            self._set_aspect_ratio(ax)
            
            # Add legend and save
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
            logging.info("3D visualization saved to: %s", save_path)
            
            if kwargs.get('show_plot', False):
                plt.show(block=True)
            
            plt.close()
            
        except Exception as e:
            logging.warning("3D matplotlib visualization failed: %s", str(e))
    
    def _configure_plot_appearance(self, ax):
        """Configure 3D plot appearance"""
        ax.grid(False)
        ax.xaxis.pane.fill = False
        ax.yaxis.pane.fill = False
        ax.zaxis.pane.fill = False
        ax.xaxis.pane.set_edgecolor('w')
        ax.yaxis.pane.set_edgecolor('w')
        ax.zaxis.pane.set_edgecolor('w')
        
        ax.xaxis.line.set_color('black')
        ax.yaxis.line.set_color('black')
        ax.zaxis.line.set_color('black')
        ax.xaxis.line.set_linewidth(1.5)
        ax.yaxis.line.set_linewidth(1.5)
        ax.zaxis.line.set_linewidth(1.5)
    
    def _plot_edges(self, ax, positions, edges):
        """Plot edges between cells"""
        if edges.shape[1] > 0:
            pos1_all = positions[edges[0]]
            pos2_all = positions[edges[1]]
            
            n_edges = edges.shape[1]
            line_segments = np.empty((n_edges * 3, 3))
            line_segments[0::3] = pos1_all
            line_segments[1::3] = pos2_all
            line_segments[2::3] = np.nan
            
            ax.plot3D(line_segments[:, 0], line_segments[:, 1], line_segments[:, 2], 
                     'gray', alpha=0.3, linewidth=0.5)
    
    def _plot_points(self, ax, positions):
        """Plot cell centers and boundary faces"""
        num_original_cells = self.mesh_manager.cells_pos.shape[0]
        original_centers = positions[:num_original_cells]
        bc_centers = positions[num_original_cells:]
        
        # Plot original cell centers
        if len(original_centers) > 0:
            ax.scatter(original_centers[:, 0], original_centers[:, 1], original_centers[:, 2],
                      c='lightblue', s=30, alpha=0.8, label=f'Cell Centers ({len(original_centers)})')
        
        # Plot boundary face centers with different colors
        if len(bc_centers) > 0:
            bc_types = self.mesh_manager.cpd_cell_type[num_original_cells:].cpu().numpy()
            unique_bc_types = np.unique(bc_types)
            
            colors = ['red', 'orange', 'green', 'purple', 'brown', 'pink']
            for i, bc_type in enumerate(unique_bc_types):
                mask = bc_types == bc_type
                bc_subset = bc_centers[mask]
                type_name = self.mesh_manager._get_boundary_type_name(bc_type)
                color = colors[i % len(colors)]
                
                ax.scatter(bc_subset[:, 0], bc_subset[:, 1], bc_subset[:, 2],
                          c=color, s=50, alpha=0.9, marker='^', 
                          label=f'BC {type_name} ({len(bc_subset)})')
    
    def _set_aspect_ratio(self, ax):
        """Set proper aspect ratio for the plot"""
        x_min, x_max = ax.get_xlim()
        y_min, y_max = ax.get_ylim()
        z_min, z_max = ax.get_zlim()
        
        x_range = x_max - x_min
        y_range = y_max - y_min
        z_range = z_max - z_min
        
        ax.set_box_aspect([x_range, y_range, z_range])
        
        padding_factor = 0.05
        x_padding = x_range * padding_factor
        y_padding = y_range * padding_factor
        z_padding = z_range * padding_factor
        
        ax.set_xlim(x_min - x_padding, x_max + x_padding)
        ax.set_ylim(y_min - y_padding, y_max + y_padding)
        ax.set_zlim(z_min - z_padding, z_max + z_padding)


class PyVistaVisualization(BaseVisualization):
    """PyVista-based visualization for ParaView compatibility"""
    
    def create_visualization(self, output_dir: str, case_name: str, **kwargs):
        """Create PyVista visualization files"""
        import pyvista as pv
        
        logging.info("Creating PyVista visualization...")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Prepare data
        mesh_pos = self.mesh_manager.mesh_pos.detach().cpu().numpy()
        edge_node_x = self.mesh_manager.edge_node_x.detach().cpu().numpy()
        
        # Create point cloud
        points = self._create_point_cloud(mesh_pos, case_name, output_dir)
        
        # Create edge visualization
        self._create_edge_visualization(mesh_pos, edge_node_x, case_name, output_dir)
        
        logging.info("PyVista visualization files created in: %s", output_dir)
    
    def _create_point_cloud(self, positions, case_name, output_dir):
        """Create point cloud visualization for mesh nodes"""
        import pyvista as pv

        points = pv.PolyData(positions)

        # Add node type information using self.mesh_manager.node_type
        if hasattr(self.mesh_manager, 'node_type') and self.mesh_manager.node_type is not None:
            # Get node types and convert to numpy
            node_types = self.mesh_manager.node_type.detach().cpu().numpy()

            # Ensure we have the right number of node types
            if len(node_types) == len(positions):
                points['NodeType'] = node_types
            else:
                logging.warning("Node type array length (%d) doesn't match positions length (%d)",
                              len(node_types), len(positions))
                # Create default node types
                points['NodeType'] = np.zeros(len(positions))
        else:
            logging.warning("node_type not available, using default values")
            points['NodeType'] = np.zeros(len(positions))

        # Add RGB colors based on node types
        rgb_colors = self._generate_colors(points['NodeType'])
        points['RGB'] = rgb_colors

        # Add size information (uniform size for all nodes)
        sizes = np.ones(len(positions)) * 0.8
        points['Size'] = sizes

        # Save points
        points_save_path = os.path.join(output_dir, f"{case_name}_nodes.vtk")
        points.save(points_save_path)
        logging.info("Graph nodes saved to: %s", points_save_path)

        return points
    
    def _create_edge_visualization(self, positions, neighbor_cell, case_name, output_dir):
        """Create edge visualization"""
        import pyvista as pv
        
        # Filter valid edges
        valid_edge_mask = neighbor_cell[0] != neighbor_cell[1]
        valid_edges = neighbor_cell[:, valid_edge_mask]
        
        if valid_edges.shape[1] > 0:
            lines = []
            for i in range(valid_edges.shape[1]):
                start_idx = valid_edges[0, i]
                end_idx = valid_edges[1, i]
                lines.extend([2, start_idx, end_idx])
            
            lines = np.array(lines)
            line_mesh = pv.PolyData(positions, lines=lines)
            line_mesh['EdgeIndex'] = np.arange(valid_edges.shape[1])
            
            edges_save_path = os.path.join(output_dir, f"{case_name}_edge_visualization.vtk")
            line_mesh.save(edges_save_path)
            logging.info("Graph edges saved to: %s", edges_save_path)
    
    def _generate_colors(self, node_types):
        """Generate RGB colors for different node types based on NodeType enum"""
        # NodeType enum values:
        # NORMAL = 0, INFLOW = 1, OUTFLOW = 2, WALL_BOUNDARY = 3,
        # PRESS_POINT = 4, IN_WALL = 5, EMPTY = 6, ZERO_GRADIENT = 7, CYCLIC = 8
        color_map = {
            0: [0.7, 0.9, 1.0],  # Light blue for NORMAL nodes
            1: [0.0, 1.0, 0.0],  # Green for INFLOW
            2: [1.0, 0.0, 0.0],  # Red for OUTFLOW
            3: [0.4, 0.4, 0.4],  # Dark gray for WALL_BOUNDARY
            4: [1.0, 1.0, 0.0],  # Yellow for PRESS_POINT
            5: [0.6, 0.3, 0.0],  # Brown for IN_WALL
            6: [1.0, 1.0, 1.0],  # White for EMPTY
            7: [1.0, 0.5, 0.0],  # Orange for ZERO_GRADIENT
            8: [1.0, 0.0, 1.0],  # Magenta for CYCLIC
        }

        rgb_colors = np.zeros((len(node_types), 3))
        for i, node_type in enumerate(node_types):
            if int(node_type) in color_map:
                rgb_colors[i] = color_map[int(node_type)]
            else:
                rgb_colors[i] = [0.5, 0.5, 0.5]  # Default gray for unknown types

        return rgb_colors
