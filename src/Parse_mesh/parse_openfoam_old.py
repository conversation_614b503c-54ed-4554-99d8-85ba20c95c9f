import sys
import os
import logging
import re

file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)
import openfoamparser as Ofpp
import numpy as np
import h5py
from Utils.utilities import NodeType # Import NodeType

import matplotlib
matplotlib.use('TkAgg')

import torch
from torch_scatter import scatter
import pyvista as pv
from Post_process.to_vtk import export_full_mesh_vtu
from Parse_mesh.parse_to_h5 import seperate_domain, compose_edge_node_x

class OpenFOAM_Manager:
    """
    Converts OpenFOAM mesh data to a structured format suitable for ML applications.
    
    This class extracts geometric and topological data from OpenFOAM meshes and
    converts them into torch tensors with consistent naming and indexing conventions.
    The processed data can be saved to HDF5 format for downstream applications.
    
    HDF5 Dataset Naming Convention:
    - node|*: Node-related datasets (e.g., node|pos)
    - face|*: Face-related datasets (e.g., face|face_center_pos, face|neighbor_cell)
    - cell|*: Cell-related datasets (e.g., cell|centroid)
    - cpd|*: Compound/boundary datasets (e.g., cpd|cell_type, cpd|neighbor_cell)
    - No prefix: Legacy/connectivity datasets (e.g., cells_node, face_node)
    
    Internal Attributes (→ HDF5 Dataset Names):
        mesh_pos → node|pos: Node coordinates [num_nodes, 3]
        faces_pos → face|face_center_pos: Face center coordinates [num_faces, 3]
        cells_pos → cell|centroid: Cell center coordinates [num_cells, 3]
        
        cells_node → cells_node: Flattened cell-to-node connectivity
        cells_node_ptr → cells_node_ptr: Pointers for cell-to-node connectivity
        cells_face → cells_face: Flattened cell-to-face connectivity
        cells_face_ptr → cells_face_ptr: Pointers for cell-to-face connectivity
        
        face_node → face_node: Flattened face-to-node connectivity
        face_node_ptr → face_node_ptr: Pointers for face-to-node connectivity
        edge_node → edge_node: Edge-to-node connectivity [num_edges, 2]
        faces_edge → face_edge: Flattened face-to-edge connectivity
        faces_edge_ptr → face_edge_ptr: Pointers for face-to-edge connectivity
        
        faces_neighbor_cell → face|neighbor_cell: Face neighbor cell indices [num_faces, 2]
        faces_type → face|face_type: Face type (0=internal, >0=boundary) [num_faces]
        faces_areas → face|face_area: Face areas [num_faces]
        cells_face_area → cells_face_area: Cell-face area mappings [total_cell_faces]
        cells_face_normal → cells_face_normal: Cell-face normal vectors [total_cell_faces, 3]
        cells_volume → cells_volume: Cell volumes [num_cells, 1]
        
        cpd_cell_type → cpd|cell_type: Compound cell types [num_compound_cells]
        cpd_neighbor_cell → cpd|neighbor_cell: Compound neighbor cells [num_compound_cells, 2]
        cpd_centroid → cpd|centroid: Compound cell centroids [num_compound_cells, 3]
    """
    def __init__(self, case_path, plt_vis=False):
        self.case_path = case_path
        mesh = Ofpp.FoamMesh(self.case_path)
        
        # visualization flag for boundary faces to cell connection 
        self.plt_vis = plt_vis
        
        # Extract all properties from mesh to self
        self.num_point = mesh.num_point
        self.num_face = mesh.num_face
        self.num_cell = mesh.num_cell + 1  # Fix for the off-by-one bug in openfoamparser
        self.num_inner_face = mesh.num_inner_face
        
        # Raw data arrays - import directly from mesh
        self.points_raw = torch.from_numpy(np.asarray(mesh.points)).float()
        self.faces_raw = mesh.faces  # List of lists (will process later)
        self.owner_raw = torch.from_numpy(np.asarray(mesh.owner)).long()
        self.neighbour_raw = torch.from_numpy(np.asarray(mesh.neighbour)).long()
        self.boundary_raw = mesh.boundary  # Dictionary (will keep as is)
        self.cell_faces_raw = mesh.cell_faces  # List of lists of face indices for each cell

        # Derived data (all will be torch tensors, saved to HDF5 with specific naming)
        self.mesh_pos = None  # Node positions [N,3] -> saved as 'node|pos'
        self.faces_pos = None  # Face center positions [F,3] -> saved as 'face|face_center_pos'
        self.cells_pos = None  # Cell center positions [C,3] -> saved as 'cell|centroid'

        self.cells_node = None  # Nodes that form each cell [N*C,1] -> saved as 'cells_node'
        self.cells_node_ptr = None  # Cell index for each entry in cells_node [N*C,1] -> saved as 'cells_node_ptr'
        self.cells_face = None  # Faces that form each cell [F*C,1] -> saved as 'cells_face'
        self.cells_face_ptr = None # Cell index for each entry in cells_face -> saved as 'cells_face_ptr'

        self.face_node = None  # Nodes that form each face [F*N,1] -> saved as 'face_node'
        self.face_node_ptr = None  # Face index for each entry in face_node [F*N,1] -> saved as 'face_node_ptr'

        self.edge_node = None  # Pairs of nodes that form each edge [E,2] -> saved as 'edge_node'
        self.faces_edge = None  # Edge indices for each face [F*E,1] -> saved as 'face_edge'
        self.faces_edge_ptr = None  # Face index for each entry in faces_edge [F*E,1] -> saved as 'face_edge_ptr'
        self.faces_neighbor_cell = None  # Cell neighbors for each face [F,2] -> saved as 'face|neighbor_cell'
        self.faces_type = None  # Type of each face (boundary conditions) [F,1] -> saved as 'face|face_type'
        self.faces_areas = None  # Areas of each face [F,1] -> saved as 'face|face_area'
        self.faces_normals = None  # Unit normal vectors for each face [F,3] (for VTU export)
        self.cells_face_area = None  # Areas of each cell face [C*F,1] -> saved as 'cells_face_area'
        self.cells_face_normal = None  # Unit normal vectors for each cell face [C*F,3] -> saved as 'cells_face_normal'
        self.cells_volume = None  # Volumes of each cell [C,1] -> saved as 'cells_volume'

        # Cyclic boundary condition data
        self.cyclic_neighbor_cell = None  # Cyclic face neighbor relationships [2, E] -> saved as 'cyclic|neighbor_cell'

        # PyVista format data
        self.pv_cells_node = None  # PyVista format cell connectivity -> used for VTU export
        self.pv_cells_type = None  # PyVista format cell types -> used for VTU export
        
        # Process the mesh data
        logging.info("Processing OpenFOAM mesh data...")
        self._extract_base_mesh_data()
        self._calculate_derived_geometric_data()
        self._calculate_connectivity_data()
        self._calculate_faces_neighbor_cell()
        self._determine_face_types()  # This now includes boundary condition parsing
        self._extract_cyclic_neighbor_relationships()  # Extract cyclic boundary relationships
        self._calculate_face_normals()
        self._calculate_edge_node_x()
        # Calculate cell volumes using divergence theorem
        # For linear function f(x) = x, div(f) = 3 in 3D
        # Volume = (1/3) * ∫ f·n dS = (1/3) * ∑ face_center · (normal * area)
        self._calculate_cells_volume()

        # Calculate compound attributes (boundary faces as virtual cells)
        logging.info("\nCalculating compound attributes...")
        self._calculate_compound_attributes()

        # Validate the processed mesh
        logging.info("\nValidating processed mesh data...")
        self._validate_mesh_consistency()

    def _extract_base_mesh_data(self):
        """Process raw data into initial tensor formats"""
        # mesh_pos is already set in __init__
        self.mesh_pos = self.points_raw  # Shape (N_points, 3)
        
        # Process faces and create face_node, face_node_ptr, edge_node, and faces_edge tensors
        face_node_list = []
        face_node_ptr_list = []
        
        # For edge processing
        edge_dict = {}  # Dictionary to map (node1, node2) -> edge_index
        edge_node_list = []  # Will store unique edges as (node1, node2) pairs
        faces_edge_list = []  # Will store edge indices for each face
        faces_edge_ptr_list = []  # Will store face indices for each edge reference
        
        # Process all faces in one pass
        for face_idx, face in enumerate(self.faces_raw):
            # Handle face_node and face_node_ptr
            face_node_list.extend(face)
            face_node_ptr_list.extend([face_idx] * len(face))
            
            # Process edges for this face
            current_face_edge = []  # Store edge indices for this face
            num_nodes = len(face)
            
            for i in range(num_nodes):
                # Get the two nodes forming this edge (ordered for consistency)
                n1, n2 = face[i], face[(i+1) % num_nodes]
                edge = tuple(sorted([int(n1), int(n2)]))  # Sort to ensure consistent edge representation
                
                # Check if this edge already exists in our dictionary
                if edge not in edge_dict:
                    # New edge: add to edge_node_list and update dictionary
                    edge_idx = len(edge_node_list)
                    edge_dict[edge] = edge_idx
                    edge_node_list.append(edge)
                else:
                    # Existing edge: get its index
                    edge_idx = edge_dict[edge]
                
                # Add this edge to the current face's edge list
                current_face_edge.append(edge_idx)
                faces_edge_ptr_list.append(face_idx)
            
            # Store all edges for this face
            faces_edge_list.extend(current_face_edge)
        
        # Convert lists to tensors
        self.face_node = torch.tensor(face_node_list, dtype=torch.long).reshape(-1, 1)
        self.face_node_ptr = torch.tensor(face_node_ptr_list, dtype=torch.long).reshape(-1, 1)
        self.edge_node = torch.tensor(edge_node_list, dtype=torch.long)
        self.faces_edge = torch.tensor(faces_edge_list, dtype=torch.long).reshape(-1, 1)
        self.faces_edge_ptr = torch.tensor(faces_edge_ptr_list, dtype=torch.long).reshape(-1, 1)

    def _calculate_derived_geometric_data(self):
        """Calculates positions of faces and cells using vectorized operations"""
        # Face centers - use scatter to compute the mean position of nodes for each face
        self.faces_pos = scatter(
            src=self.mesh_pos[self.face_node.squeeze()], 
            index=self.face_node_ptr.squeeze(),
            dim=0, 
            reduce='mean'
        )  # Shape (N_faces, 3)

    def _calculate_connectivity_data(self):
        """Calculates connectivity arrays using torch tensors"""
        num_cell = self.num_cell
        
        # First, gather cells_face information
        cells_face_list = []
        cells_face_index_list = []
        
        for cell_idx in range(num_cell):
            cell_face_indices = self.cell_faces_raw[cell_idx]
            cells_face_list.extend(cell_face_indices)
            cells_face_index_list.extend([cell_idx] * len(cell_face_indices))
            
        self.cells_face = torch.tensor(cells_face_list, dtype=torch.long).squeeze()
        self.cells_face_ptr = torch.tensor(cells_face_index_list, dtype=torch.long).squeeze()

        # Now derive cells_node from cells_face and face_node more efficiently
        # Directly build cells_node and cells_node_ptr lists in one pass
        cells_node_list = []
        cells_node_ptr_list = []

        # Generate PyVista format cells_node and cells_type at the same time
        pv_cells_node_list = []
        pv_cells_type_list = []

        for cell_idx in range(num_cell):
            # Get all faces for this cell
            _cell_faces = self.cell_faces_raw[cell_idx]
            num_faces = len(_cell_faces)

            # Collect unique nodes from all these faces using a set
            nodes_set = set()
            for face_idx in _cell_faces:
                face_nodes = self.faces_raw[face_idx]
                nodes_set.update(face_nodes)

            # Add nodes to the lists (original logic)
            sub_face_node = list(nodes_set)
            cells_node_list.extend(sub_face_node)
            cells_node_ptr_list.extend([cell_idx] * len(sub_face_node))
            
            # Generate PyVista format data for this cell
            # Use POLYHEDRON format for all cell types to avoid vertex ordering issues
            pv_cells_type_list.append(pv.CellType.POLYHEDRON)
            
            # Count total items needed for polyhedron format
            face_connectivity = [num_faces]  # Start with number of faces
            
            for face_idx in _cell_faces:
                face_nodes = self.faces_raw[face_idx]
                face_connectivity.append(len(face_nodes))  # Number of nodes in this face
                face_connectivity.extend(face_nodes)  # Node indices
            
            # Add total count and face connectivity
            pv_cells_node_list.extend([len(face_connectivity)] + face_connectivity)
            
        self.cells_node = torch.tensor(cells_node_list, dtype=torch.long).squeeze()
        self.cells_node_ptr = torch.tensor(cells_node_ptr_list, dtype=torch.long).squeeze()
        
        # Store PyVista format data
        self.pv_cells_node = torch.tensor(pv_cells_node_list, dtype=torch.long)
        self.pv_cells_type = torch.tensor(pv_cells_type_list, dtype=torch.long)
        
        # Calculate cell centers from node positions using cells_node
        self.cells_pos = scatter(
            src=self.mesh_pos[self.cells_node], 
            index=self.cells_node_ptr,
            dim=0, 
            reduce='mean'
        )  # Shape (N_cells, 3)

    def _calculate_faces_neighbor_cell(self):
        """Calculates faces_neighbor_cell array using vectorized operations"""
        num_total_faces = len(self.faces_raw)
        
        # Based on your understanding of the openfoamparser implementation:
        # - For internal faces: Use owner_raw and neighbour_raw
        # - For boundary faces: Duplicate the owner cell
        
        # Initialize the faces_neighbor_cell tensor
        self.faces_neighbor_cell = torch.zeros((num_total_faces, 2), dtype=torch.long)
        
        # Set the first neighbor (owner) for all faces
        self.faces_neighbor_cell[:, 0] = self.owner_raw
        
        # Find internal and boundary faces
        all_face_indices = set(range(num_total_faces))
        boundary_face_indices = set()
        
        # Debug the boundary data structure
        logging.debug("Debug - Boundary data type: %s", type(self.boundary_raw))
        logging.debug("Debug - Boundary data keys: %s", list(self.boundary_raw.keys())[:3] if hasattr(self.boundary_raw, 'keys') else "No keys method")
        logging.debug("Debug - First boundary item: %s", next(iter(self.boundary_raw.items())) if hasattr(self.boundary_raw, 'items') else "No items method")
        
        # Process boundary faces
        for b_name, b_data in self.boundary_raw.items():
            # Handle both dictionary and namedtuple formats
            if isinstance(b_data, dict):
                start_face = b_data['startFace'] 
                n_faces = b_data['nFaces']
            elif hasattr(b_data, 'start') and hasattr(b_data, 'num'):
                # Handle namedtuple format from openfoamparser
                start_face = b_data.start
                n_faces = b_data.num
            else:
                logging.warning("Warning: Unknown boundary data format for %s: %s", b_name, b_data)
                continue
                
            boundary_face_indices.update(range(start_face, start_face + n_faces))
            
        internal_face_indices = list(all_face_indices - boundary_face_indices)
        boundary_face_indices = list(boundary_face_indices)
        
        # Set the second neighbor
        # For internal faces: Use neighbour_raw
        if len(internal_face_indices) == len(self.neighbour_raw):
            # Most common case - neighbour_raw has exactly the neighbors for internal faces
            internal_face_indices_tensor = torch.tensor(internal_face_indices, dtype=torch.long)
            self.faces_neighbor_cell[internal_face_indices_tensor, 1] = self.neighbour_raw
        else:
            # Fallback case - len(neighbour_raw) may not match internal faces count
            # Use the first len(neighbour_raw) faces as internal faces
            self.faces_neighbor_cell[:len(self.neighbour_raw), 1] = self.neighbour_raw
            
        # For boundary faces: Set second neighbor to be the same as the owner
        boundary_face_indices_tensor = torch.tensor(boundary_face_indices, dtype=torch.long)
        self.faces_neighbor_cell[boundary_face_indices_tensor, 1] = self.faces_neighbor_cell[boundary_face_indices_tensor, 0]

    def _determine_face_types(self):
        """Determines faces types based on boundary conditions from field files"""
        logging.info("Determining face types from boundary conditions...")
        
        # First parse boundary conditions from field files
        self._parse_boundary_conditions()
        
        num_total_faces = len(self.faces_raw)
        
        # Initialize ALL faces as internal (NORMAL)
        # This ensures internal faces stay as NORMAL
        self.faces_type = torch.full((num_total_faces, 1), NodeType.NORMAL, dtype=torch.long)
        
        # Process boundary patches only
        # Internal faces will remain as NORMAL
        for boundary_name, boundary_props in self.boundary_raw.items():
            # Handle both dictionary and namedtuple formats
            if isinstance(boundary_props, dict):
                start_face = boundary_props['startFace']
                n_faces = boundary_props['nFaces']
                geom_type = boundary_props['type']
            elif hasattr(boundary_props, 'start') and hasattr(boundary_props, 'num'):
                # Handle namedtuple format from openfoamparser
                start_face = boundary_props.start
                n_faces = boundary_props.num
                geom_type = boundary_props.type if hasattr(boundary_props, 'type') else "unknown"
            else:
                logging.warning("Warning: Unknown boundary data format for %s: %s", boundary_name, boundary_props)
                continue
                
            # Handle byte strings (common in Python 3 when reading binary files)
            if isinstance(geom_type, bytes):
                geom_type = geom_type.decode('utf-8')
                
            # Handle additional layer of byte strings
            if isinstance(boundary_name, bytes):
                boundary_name = boundary_name.decode('utf-8')
            
            # Get boundary condition types from field files
            u_bc_type = 'unknown'
            p_bc_type = 'unknown'
            
            if 'U' in self.boundary_conditions and boundary_name in self.boundary_conditions['U']:
                u_bc_type = self.boundary_conditions['U'][boundary_name]['type']
            
            if 'p' in self.boundary_conditions and boundary_name in self.boundary_conditions['p']:
                p_bc_type = self.boundary_conditions['p'][boundary_name]['type']
            
            # Map to NodeType using the new mapping function
            node_type = self._map_openfoam_bc_to_nodetype(boundary_name, u_bc_type, p_bc_type, geom_type)
            
            logging.info("  %s: geom_type=%s, U_bc=%s, p_bc=%s -> %s", boundary_name, geom_type, u_bc_type, p_bc_type, node_type.name)
            
            # Update faces types for this boundary patch
            self.faces_type[start_face:start_face + n_faces] = node_type
        
        # Print boundary condition statistics
        logging.info("\nBoundary condition statistics:")
        unique_types, counts = torch.unique(self.faces_type, return_counts=True)
        for node_type_val, count in zip(unique_types, counts):
            node_type_name = NodeType(node_type_val.item()).name
            logging.info("  %s: %d faces", node_type_name, count.item())
            
        logging.info("Total faces: %d (internal: %d, boundary: %d)", num_total_faces, self.num_inner_face, num_total_faces - self.num_inner_face)

    def _extract_cyclic_neighbor_relationships(self):
        """
        Extract cyclic boundary face relationships and convert to cell connectivity.
        
        For cyclic boundaries in OpenFOAM, faces are paired between two cyclic patches.
        This method finds these paired faces and creates cell-to-cell connectivity
        across the cyclic boundary, treating it as additional cell neighbors.
        
        Returns:
            Sets self.cyclic_neighbor_cell: [2, E] tensor where E is the number of cyclic connections
        """
        logging.info("Extracting cyclic boundary relationships...")
        
        # Initialize empty list to store cyclic connections
        cyclic_connections = []
        
        # Find all cyclic boundary patches
        cyclic_patches = {}
        for boundary_name, boundary_props in self.boundary_raw.items():
            # Handle both dictionary and namedtuple formats
            if isinstance(boundary_props, dict):
                geom_type = boundary_props.get('type', 'unknown')
                start_face = boundary_props.get('startFace', 0)
                n_faces = boundary_props.get('nFaces', 0)
                neighbour_patch = boundary_props.get('neighbourPatch', None)
            elif hasattr(boundary_props, 'start') and hasattr(boundary_props, 'num'):
                # Handle namedtuple format from openfoamparser
                geom_type = boundary_props.type if hasattr(boundary_props, 'type') else "unknown"
                start_face = boundary_props.start
                n_faces = boundary_props.num
                # Get neighbourPatch from the raw boundary data by parsing the boundary file
                neighbour_patch = self._get_neighbour_patch_from_boundary_file(boundary_name)
            else:
                logging.warning("Warning: Unknown boundary data format for %s", boundary_name)
                continue
            
            # Handle byte strings (common in Python 3 when reading binary files)
            if isinstance(geom_type, bytes):
                geom_type = geom_type.decode('utf-8')
            if isinstance(boundary_name, bytes):
                boundary_name = boundary_name.decode('utf-8')
                
            # Check if this is a cyclic boundary
            if geom_type == 'cyclic':
                cyclic_patches[boundary_name] = {
                    'start_face': start_face,
                    'n_faces': n_faces,
                    'neighbour_patch': neighbour_patch
                }
                logging.info("  Found cyclic patch '%s': faces %d-%d, neighbour='%s'", 
                           boundary_name, start_face, start_face + n_faces - 1, neighbour_patch)
        
        # Process cyclic patch pairs
        processed_patches = set()
        for patch_name, patch_info in cyclic_patches.items():
            if patch_name in processed_patches:
                continue
                
            neighbour_name = patch_info['neighbour_patch']
            if neighbour_name not in cyclic_patches:
                logging.warning("Warning: Neighbour patch '%s' for cyclic patch '%s' not found", 
                              neighbour_name, patch_name)
                continue
            
            neighbour_info = cyclic_patches[neighbour_name]
            
            # Validate that the patches have the same number of faces
            if patch_info['n_faces'] != neighbour_info['n_faces']:
                logging.warning("Warning: Cyclic patches '%s' and '%s' have different face counts (%d vs %d)", 
                              patch_name, neighbour_name, patch_info['n_faces'], neighbour_info['n_faces'])
                continue
            
            # Extract face ranges for both patches
            patch1_start = patch_info['start_face']
            patch1_end = patch1_start + patch_info['n_faces']
            patch1_faces = torch.arange(patch1_start, patch1_end, dtype=torch.long)
            
            patch2_start = neighbour_info['start_face']
            patch2_end = patch2_start + neighbour_info['n_faces']
            patch2_faces = torch.arange(patch2_start, patch2_end, dtype=torch.long)
            
            # Get owner cells for both patches (using first neighbor since boundary faces have same owner/neighbor)
            patch1_cells = self.faces_neighbor_cell[patch1_faces, 0]  # [n_faces]
            patch2_cells = self.faces_neighbor_cell[patch2_faces, 0]  # [n_faces]
            
            # Create cyclic connections: each face in patch1 connects to corresponding face in patch2
            # This creates bidirectional cell-to-cell connections across the cyclic boundary
            cyclic_connections.extend([
                torch.stack([patch1_cells, patch2_cells], dim=0),  # patch1 -> patch2
                torch.stack([patch2_cells, patch1_cells], dim=0)   # patch2 -> patch1
            ])
            
            logging.info("  Created %d bidirectional cyclic connections between '%s' and '%s'", 
                        patch_info['n_faces'], patch_name, neighbour_name)
            
            # Mark both patches as processed
            processed_patches.add(patch_name)
            processed_patches.add(neighbour_name)
        
        # Combine all cyclic connections
        if cyclic_connections:
            self.cyclic_neighbor_cell = torch.cat(cyclic_connections, dim=1)  # [2, total_connections]
            logging.info("  Total cyclic connections: %d", self.cyclic_neighbor_cell.shape[1])
        else:
            # No cyclic boundaries found, create empty tensor
            self.cyclic_neighbor_cell = torch.empty((2, 0), dtype=torch.long)
            logging.info("  No cyclic boundaries found")
        
        # Validate cyclic connections
        self._validate_cyclic_connections()
    
    def _get_neighbour_patch_from_boundary_file(self, patch_name):
        """
        Parse the boundary file to extract neighbourPatch information for cyclic boundaries.
        
        This is needed because openfoamparser might not extract all boundary properties.
        """
        boundary_file = os.path.join(self.case_path, 'constant', 'polyMesh', 'boundary')
        if not os.path.exists(boundary_file):
            logging.warning("Boundary file not found: %s", boundary_file)
            return None
            
        try:
            with open(boundary_file, 'r') as f:
                content = f.read()
            
            # Handle byte strings
            if isinstance(patch_name, bytes):
                patch_name = patch_name.decode('utf-8')
            
            # Find the patch section using a more robust regex pattern
            # This handles nested braces and various formatting
            patch_pattern = rf'{re.escape(patch_name)}\s*\{{([^{{}}]*(?:\{{[^{{}}]*\}}[^{{}}]*)*)\}}'
            patch_match = re.search(patch_pattern, content, re.MULTILINE | re.DOTALL)
            
            if patch_match:
                patch_content = patch_match.group(1)
                logging.debug("Found patch content for %s: %s", patch_name, patch_content[:100])
                
                # Extract neighbourPatch with more flexible pattern
                neighbour_match = re.search(r'neighbourPatch\s+(\w+)\s*;', patch_content)
                if neighbour_match:
                    neighbour_name = neighbour_match.group(1)
                    logging.debug("Found neighbourPatch for %s: %s", patch_name, neighbour_name)
                    return neighbour_name
                else:
                    logging.debug("No neighbourPatch found in patch content for %s", patch_name)
            else:
                logging.debug("No patch section found for %s", patch_name)
            
            return None
            
        except Exception as e:
            logging.warning("Error parsing boundary file for patch %s: %s", patch_name, e)
            return None
    
    def _validate_cyclic_connections(self):
        """Validate the extracted cyclic connections"""
        if self.cyclic_neighbor_cell.shape[1] == 0:
            return
            
        logging.info("  Validating cyclic connections...")
        
        # Check for self-loops (should not exist)
        self_loops = (self.cyclic_neighbor_cell[0] == self.cyclic_neighbor_cell[1]).sum().item()
        if self_loops > 0:
            logging.warning("    Warning: Found %d self-loops in cyclic connections", self_loops)
        else:
            logging.info("    ✓ No self-loops found")
        
        # Check that all referenced cells exist
        max_cell_idx = torch.max(self.cyclic_neighbor_cell).item()
        if max_cell_idx >= self.num_cell:
            logging.warning("    Warning: Cyclic connections reference cell index %d >= num_cells %d", 
                          max_cell_idx, self.num_cell)
        else:
            logging.info("    ✓ All cell indices are valid: max_index=%d < num_cells=%d", 
                        max_cell_idx, self.num_cell)
        
        # Print connection statistics
        unique_senders = torch.unique(self.cyclic_neighbor_cell[0]).shape[0]
        unique_receivers = torch.unique(self.cyclic_neighbor_cell[1]).shape[0]
        logging.info("    Unique sender cells: %d", unique_senders)
        logging.info("    Unique receiver cells: %d", unique_receivers)
        logging.info("    Total connections: %d", self.cyclic_neighbor_cell.shape[1])
       
       
    def _calculate_compound_attributes(self):
        """Generate compound attributes (boundary faces as virtual cells)
        
        Creates compound arrays that treat boundary faces as virtual cells:
        - cpd_cell_type: Cell types including boundary face types -> saved as 'cpd|cell_type'
        - cpd_neighbor_cell: Modified neighbor relationships -> saved as 'cpd|neighbor_cell' 
        - cpd_centroid: Cell centers + boundary face centers -> saved as 'cpd|centroid'
        """
        logging.info("Calculating compound attributes for boundary faces...")
        
        # Use correct attribute names from self
        face_type = self.faces_type.long().squeeze()
        neighbor_cell = self.faces_neighbor_cell.long().T  # Transpose to get [2, N_faces] format
        face_center_pos = self.faces_pos.to(torch.float32)
        centroid = self.cells_pos.to(torch.float32)
        
        # Find boundary faces (non-NORMAL faces)
        BC_face_mask = ~(face_type == NodeType.NORMAL).squeeze()
        BC_face_center_pos = face_center_pos[BC_face_mask]
        BC_face_center_pos_ptr = torch.arange(BC_face_center_pos.shape[0]) + centroid.shape[0]
        
        # Create compound cell type: original cells + boundary face types
        cell_type = torch.cat((
            torch.full((centroid.shape[0],), NodeType.NORMAL, dtype=torch.long),
            face_type[BC_face_mask]
        ), dim=0)
        
        # Validate boundary face configuration
        if not (neighbor_cell[0, BC_face_mask] == neighbor_cell[1, BC_face_mask]).all():
            raise ValueError("Boundary faces should have same owner and neighbor cells")
        
        # Create compound neighbor cell array: modify neighbor indices for boundary faces
        cpd_neighbor_cell = neighbor_cell.clone()
        cpd_neighbor_cell[1, BC_face_mask] = BC_face_center_pos_ptr
        
        # Create compound centroid: original cell centers + boundary face centers
        cpd_centroid = torch.cat((centroid, BC_face_center_pos), dim=0)
        
        # Store compound attributes
        self.cpd_cell_type = cell_type
        self.cpd_neighbor_cell = cpd_neighbor_cell
        self.cpd_centroid = cpd_centroid
        
        logging.info("  Original cells: %d", centroid.shape[0])
        logging.info("  Boundary faces treated as cells: %d", BC_face_center_pos.shape[0])
        logging.info("  Total compound cells: %d", cpd_centroid.shape[0])
        
        # Execute 3D visualization
        if self.plt_vis:
            '''以下两个函数是重复的功能，一个是调用matplotlib，一个是调用pyvista''' 
            # self._visualize_3d_graph_structure()

            # Also create PyVista visualization for ParaView
            self.visualize_3d_graph_with_pyvista()

    def _visualize_3d_graph_structure(self):
        """3D visualization of mesh structure using matplotlib"""
        try:
            import matplotlib.pyplot as plt
            from mpl_toolkits.mplot3d import Axes3D
            import numpy as np
            
            logging.info("  Creating 3D visualization...")
            
            # Get case name for file naming
            case_name = os.path.basename(self.case_path)
            
            # Create output directory
            output_dir = os.path.join(self.case_path, 'Graph_visualization')
            os.makedirs(output_dir, exist_ok=True)
            save_path = os.path.join(output_dir, f"cell_graph_3D_{case_name}.png")
            
            # Prepare data
            cpd_centroid_np = self.cpd_centroid.detach().cpu().numpy()
            cpd_neighbor_cell_np = self.cpd_neighbor_cell.detach().cpu().numpy()
            cyclic_neighbor_cell = self.cyclic_neighbor_cell.detach().cpu().numpy()     

            # Filter out self-loops for cleaner visualization
            valid_edge_mask = cpd_neighbor_cell_np[0] != cpd_neighbor_cell_np[1]
            valid_edges = cpd_neighbor_cell_np[:, valid_edge_mask]
            
            # Create 3D plot
            fig = plt.figure(figsize=(16, 12))
            ax = fig.add_subplot(111, projection='3d')
            
            # Disable grid and set clean appearance
            ax.grid(False)
            ax.xaxis.pane.fill = False
            ax.yaxis.pane.fill = False
            ax.zaxis.pane.fill = False
            ax.xaxis.pane.set_edgecolor('w')
            ax.yaxis.pane.set_edgecolor('w')
            ax.zaxis.pane.set_edgecolor('w')
            
            # Set axis appearance
            ax.xaxis.line.set_color('black')
            ax.yaxis.line.set_color('black')
            ax.zaxis.line.set_color('black')
            ax.xaxis.line.set_linewidth(1.5)
            ax.yaxis.line.set_linewidth(1.5)
            ax.zaxis.line.set_linewidth(1.5)
            
            # Plot edges between cell centers using vectorized operations
            if valid_edges.shape[1] > 0:
                # Get positions for all edge endpoints at once
                pos1_all = cpd_centroid_np[valid_edges[0]]  # Shape: (n_edges, 3)
                pos2_all = cpd_centroid_np[valid_edges[1]]  # Shape: (n_edges, 3)
                
                # Create line segments for all edges
                # Each line segment needs [start_point, end_point, None] for matplotlib
                n_edges = valid_edges.shape[1]
                line_segments = np.empty((n_edges * 3, 3))
                line_segments[0::3] = pos1_all  # Start points
                line_segments[1::3] = pos2_all  # End points  
                line_segments[2::3] = np.nan    # Separators (creates line breaks)
                
                # Plot all edges at once
                ax.plot3D(line_segments[:, 0], line_segments[:, 1], line_segments[:, 2], 
                         'gray', alpha=0.3, linewidth=0.5)
            
            # Separate original cells and boundary face centers
            num_original_cells = self.cells_pos.shape[0]
            original_centers = cpd_centroid_np[:num_original_cells]
            bc_centers = cpd_centroid_np[num_original_cells:]
            
            # Plot original cell centers
            if len(original_centers) > 0:
                ax.scatter(original_centers[:, 0], original_centers[:, 1], original_centers[:, 2],
                          c='lightblue', s=30, alpha=0.8, label=f'Cell Centers ({len(original_centers)})')
            
            # Plot boundary face centers with different colors based on type
            if len(bc_centers) > 0:
                bc_types = self.cpd_cell_type[num_original_cells:].cpu().numpy()
                unique_bc_types = np.unique(bc_types)
                
                colors = ['red', 'orange', 'green', 'purple', 'brown', 'pink']
                for i, bc_type in enumerate(unique_bc_types):
                    mask = bc_types == bc_type
                    bc_subset = bc_centers[mask]
                    type_name = self._get_boundary_type_name(bc_type)
                    color = colors[i % len(colors)]
                    
                    ax.scatter(bc_subset[:, 0], bc_subset[:, 1], bc_subset[:, 2],
                              c=color, s=50, alpha=0.9, marker='^', 
                              label=f'BC {type_name} ({len(bc_subset)})')
            
            # Set labels and title
            ax.set_xlabel('X')
            ax.set_ylabel('Y')
            ax.set_zlabel('Z')
            ax.set_title(f'3D Mesh Structure: {case_name}\n'
                        f'Cells: {num_original_cells}, BC Faces: {len(bc_centers)}, Edges: {valid_edges.shape[1]}')
            
            # Set proper aspect ratio to maintain mesh proportions
            # Use the actual data ranges to set reasonable limits
            x_min, x_max = ax.get_xlim()
            y_min, y_max = ax.get_ylim()
            z_min, z_max = ax.get_zlim()
            
            # Calculate the actual ranges
            x_range = x_max - x_min
            y_range = y_max - y_min
            z_range = z_max - z_min
            
            # Set aspect ratio based on actual data proportions
            # This preserves the true geometric proportions of the mesh
            ax.set_box_aspect([x_range, y_range, z_range])
            
            # Add some padding around the data for better visualization
            padding_factor = 0.05  # 5% padding
            x_padding = x_range * padding_factor
            y_padding = y_range * padding_factor
            z_padding = z_range * padding_factor
            
            ax.set_xlim(x_min - x_padding, x_max + x_padding)
            ax.set_ylim(y_min - y_padding, y_max + y_padding)
            ax.set_zlim(z_min - z_padding, z_max + z_padding)
            
            # Add legend
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            
            # Adjust layout
            plt.tight_layout()
            
            # Save plot
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logging.info("  3D visualization saved to: %s", save_path)
            
            # Show plot (optional, comment out if not needed)
            plt.show(block=True)
            
            plt.close()
            
            # Print statistics
            logging.info("  Graph statistics:")
            logging.info("    - Original cell centers: %d", num_original_cells)
            logging.info("    - Boundary face centers: %d", len(bc_centers))
            logging.info("    - Total compound cells: %d", len(cpd_centroid_np))
            logging.info("    - Valid edges: %d", valid_edges.shape[1])
            
        except Exception as e:
            logging.warning("  3D visualization failed: %s", str(e))
            logging.info("  Continuing with mesh processing...")
            import traceback
            traceback.print_exc()
    
    def visualize_3d_graph_with_pyvista(self):
        """3D visualization of mesh structure using PyVista for ParaView compatibility
        
        Generates two VTK files: one for points (vertices) and one for edges (lines).
        This allows separate control of point and line visualization in ParaView.
        """
        
        import pyvista as pv
        import numpy as np
        
        logging.info("  Creating 3D visualization with PyVista...")
        
        # Get case name for file naming
        case_name = os.path.basename(self.case_path)
        
        # Create output directory
        output_dir = os.path.join(self.case_path, 'Graph_visualization')
        os.makedirs(output_dir, exist_ok=True)
        
        # Prepare data
        cpd_centroid_np = self.cpd_centroid.detach().cpu().numpy()
        cpd_neighbor_cell_np = self.cpd_neighbor_cell.detach().cpu().numpy()

        # Filter out self-loops for cleaner visualization
        valid_edge_mask = cpd_neighbor_cell_np[0] != cpd_neighbor_cell_np[1]
        valid_edges = cpd_neighbor_cell_np[:, valid_edge_mask]
        
        # Create PyVista point cloud for cell centers
        points = pv.PolyData(cpd_centroid_np)
        
        # Add cell type information as point data
        num_original_cells = self.cells_pos.shape[0]
        cell_types = np.zeros(len(cpd_centroid_np))
        
        # Assign different values for different cell types
        cell_types[:num_original_cells] = 0  # Original cells
        if len(cpd_centroid_np) > num_original_cells:
            bc_types = self.cpd_cell_type[num_original_cells:].cpu().numpy()
            cell_types[num_original_cells:] = bc_types + 1  # Boundary faces (offset by 1)
        
        points['CellType'] = cell_types
        
        # Create color mapping for different cell types
        color_map = {}
        type_names = {}
        
        # Original cells
        color_map[0] = [0.7, 0.9, 1.0]  # Light blue
        type_names[0] = "Original_Cells"
        
        # Boundary face types
        colors = [
            [1.0, 0.0, 0.0],  # Red
            [1.0, 0.5, 0.0],  # Orange  
            [0.0, 0.8, 0.0],  # Green
            [0.5, 0.0, 1.0],  # Purple
            [0.6, 0.3, 0.0],  # Brown
            [1.0, 0.4, 0.7],  # Pink
        ]
        
        unique_bc_types = np.unique(self.cpd_cell_type[num_original_cells:].cpu().numpy()) if len(cpd_centroid_np) > num_original_cells else []
        
        for i, bc_type in enumerate(unique_bc_types):
            adjusted_type = bc_type + 1  # Offset by 1 since 0 is for original cells
            color_map[adjusted_type] = colors[i % len(colors)]
            type_names[adjusted_type] = self._get_boundary_type_name(bc_type)
        
        # Add RGB colors as point data
        rgb_colors = np.zeros((len(cpd_centroid_np), 3))
        for i, cell_type in enumerate(cell_types):
            if int(cell_type) in color_map:
                rgb_colors[i] = color_map[int(cell_type)]
            else:
                rgb_colors[i] = [0.5, 0.5, 0.5]  # Default gray
        
        points['RGB'] = rgb_colors
        
        # Add size information for different visualization
        sizes = np.ones(len(cpd_centroid_np))
        sizes[:num_original_cells] = 0.8  # Smaller for original cells
        sizes[num_original_cells:] = 1.2  # Larger for boundary faces
        points['Size'] = sizes
        
        # Save points as VTK file
        points_save_path = os.path.join(output_dir, f"{case_name}_centroids.vtk")
        points.save(points_save_path)
        logging.info("  Graph points saved to: %s", points_save_path)
        
        # Save edges as separate VTK file if they exist
        if valid_edges.shape[1] > 0:
            # Create lines connecting cells
            lines = []
            for i in range(valid_edges.shape[1]):
                start_idx = valid_edges[0, i]
                end_idx = valid_edges[1, i]
                lines.extend([2, start_idx, end_idx])  # 2 points per line
            
            lines = np.array(lines)
            line_mesh = pv.PolyData(cpd_centroid_np, lines=lines)
            
            # Add edge data for visualization
            line_mesh['EdgeIndex'] = np.arange(valid_edges.shape[1])
            
            # Save edges as VTK file
            edges_save_path = os.path.join(output_dir, f"{case_name}_neighbor_cell.vtk")
            line_mesh.save(edges_save_path)
            logging.info("  Graph edges saved to: %s", edges_save_path)
        else:
            logging.info("  No valid edges to save")
        
        # Print statistics
        logging.info("  PyVista visualization statistics:")
        logging.info("    - Original cell centers: %d", num_original_cells)
        logging.info("    - Boundary face centers: %d", len(cpd_centroid_np) - num_original_cells)
        logging.info("    - Total compound cells: %d", len(cpd_centroid_np))
        logging.info("    - Valid edges: %d", valid_edges.shape[1] if valid_edges.shape[1] > 0 else 0)

    def _calculate_face_normals(self):
        """Calculates unit normal vectors and areas for each face and cell face using vectorized operations"""
        logging.info("Calculating face normals and areas...")
        
        faces_centers = self.faces_pos
        
        # For each face, calculate normal using vectorized cross products
        num_faces = len(self.faces_raw)
        faces_normals = torch.zeros(num_faces, 3, dtype=torch.float32)
        faces_areas = torch.zeros(num_faces, dtype=torch.float32)
        
        # Process each face using vectorized operations where possible
        for face_idx, face_vertices in enumerate(self.faces_raw):
            if len(face_vertices) < 3:
                continue
                
            # Get face vertices positions
            vertices = self.mesh_pos[face_vertices]  # [n_vertices, 3]
            face_center = faces_centers[face_idx]  # [3]
            
            # Create vectors from face center to each vertex
            center_to_vertices = vertices - face_center.unsqueeze(0)  # [n_vertices, 3]
            
            # Create vectors between consecutive vertices (edges)
            n_vertices = len(face_vertices)
            v1 = center_to_vertices  # [n_vertices, 3]
            v2 = torch.roll(center_to_vertices, -1, dims=0)  # [n_vertices, 3] - next vertex
            
            # Calculate cross products for all triangles at once
            cross_products = torch.cross(v1, v2, dim=1)  # [n_vertices, 3]
            
            # Sum all cross products to get face normal
            normal_sum = torch.sum(cross_products, dim=0)  # [3]
            
            # Calculate face area as sum of triangle areas
            triangle_areas = 0.5 * torch.norm(cross_products, dim=1)  # [n_vertices]
            face_area = torch.sum(triangle_areas)
            
            # Normalize normal vector
            norm = torch.norm(normal_sum)
            if norm > 1e-10:
                faces_normals[face_idx] = normal_sum / norm
            else:
                faces_normals[face_idx] = torch.zeros(3, dtype=torch.float32)
                
            faces_areas[face_idx] = face_area
        
        # Store face areas and normals
        self.faces_areas = faces_areas
        self.faces_normals = faces_normals  # Store face normals for VTU export
        
        # Now calculate cells-face normals and areas using vectorized operations
        # Get cell centers for all cells that have faces
        cells_centers = self.cells_pos[self.cells_face_ptr]  # [total_cell_faces, 3]
        faces_centers_expanded = faces_centers[self.cells_face]  # [total_cell_faces, 3]
        
        # Calculate vectors from cell centers to face centers
        cell_to_face_vectors = faces_centers_expanded - cells_centers  # [total_cell_faces, 3]
        
        # Get face normals for each cell-face pair
        faces_normals_expanded = faces_normals[self.cells_face]  # [total_cell_faces, 3]
        
        # Check orientation: if dot product is negative, flip normal
        dot_products = torch.sum(cell_to_face_vectors * faces_normals_expanded, dim=1)  # [total_cell_faces]
        flip_mask = dot_products < 0
        
        # Apply flipping where needed
        cells_face_normal = faces_normals_expanded.clone()
        cells_face_normal[flip_mask] = -cells_face_normal[flip_mask]
        
        # Get face areas for each cell-face pair
        cells_face_area = faces_areas[self.cells_face]  # [total_cell_faces]
        
        # Store results
        self.cells_face_normal = cells_face_normal
        self.cells_face_area = cells_face_area.reshape(-1, 1)
        
        logging.info("  Calculated normals and areas for %d faces", num_faces)
        logging.info("  Cell-face pairs: %d", len(self.cells_face_normal))
        
        # Validate face normals using flux closure test
        self._validate_face_normals()

    def _validate_face_normals(self):
        """
        Validate that cells-face normals and areas are correctly computed
        by checking that the sum of (face normal × face area) for each cell is approximately zero.
        This uses the flux closure principle.
        """
        logging.info("  Validating face normals using flux closure test...")
        
        # Calculate weighted normals (normal * area)
        weighted_normals = self.cells_face_normal * self.cells_face_area  # [total_cell_faces, 3]
        
        # Sum for each cell using cells_face_ptr
        flux_sum = scatter(
            src=weighted_normals,
            index=self.cells_face_ptr,
            dim=0,
            reduce='sum'
        )  # [num_cells, 3]
        
        # Calculate the magnitude of the sum for each cell
        flux_sum_magnitude = torch.norm(flux_sum, dim=1)  # [num_cells]
        
        # Calculate cell face area sum for normalization
        cell_area_sum = scatter(
            src=self.cells_face_area.squeeze(),
            index=self.cells_face_ptr,
            dim=0,
            reduce='sum'
        )  # [num_cells]
        
        # Normalize by total face area of each cell
        normalized_flux = flux_sum_magnitude / cell_area_sum
        
        # Print statistics
        logging.info("    Flux closure test results (should be close to zero):")
        logging.info("    Min normalized flux: %.8f", torch.min(normalized_flux).item())
        logging.info("    Max normalized flux: %.8f", torch.max(normalized_flux).item())
        logging.info("    Mean normalized flux: %.8f", torch.mean(normalized_flux).item())
        logging.info("    Median normalized flux: %.8f", torch.median(normalized_flux).item())
        
        # Count cells with closure error above threshold
        threshold = 1e-5
        bad_cells = torch.sum(normalized_flux > threshold).item()
        logging.info("    Cells with normalized flux > %s: %d out of %d (%.2f%%)", threshold, bad_cells, len(normalized_flux), bad_cells/len(normalized_flux)*100)
        
        # Show worst cases if any
        if bad_cells > 0:
            worst_indices = torch.argsort(normalized_flux, descending=True)[:min(5, bad_cells)]
            logging.info("    Worst cell flux closure values:")
            for i, idx in enumerate(worst_indices):
                logging.info("      Cell %d: %.8f", idx, normalized_flux[idx].item())
    
    def _validate_mesh_consistency(self):
        """Validate data consistency in the mesh"""
        logging.info("Validating mesh consistency:")
        
        # Check basic dimensions
        logging.info("  Number of nodes: %d", self.num_point)
        logging.info("  Number of faces: %d", self.num_face)
        logging.info("  Number of cells: %d", self.num_cell)
        logging.info("  Number of internal faces: %d", self.num_inner_face)
        logging.info("  Number of edges: %d", len(self.edge_node))
        
        # Check connectivity data sizes
        logging.info("  cells_node entries: %d", len(self.cells_node))
        logging.info("  cells_face entries: %d", len(self.cells_face))
        logging.info("  face_node entries: %d", len(self.face_node))
        logging.info("  faces_edge entries: %d", len(self.faces_edge))
        
        # Check if all indices are within valid range
        max_node_idx = torch.max(self.cells_node).item()
        if max_node_idx >= self.num_point:
            logging.warning("  WARNING: cells_node contains index %d >= num_points %d", max_node_idx, self.num_point)
        else:
            logging.info("  ✓ cells_node indices valid: max_index=%d < num_points=%d", max_node_idx, self.num_point)
        
        max_face_idx = torch.max(self.cells_face).item()
        if max_face_idx >= self.num_face:
            logging.warning("  WARNING: cells_face contains index %d >= num_faces %d", max_face_idx, self.num_face)
        else:
            logging.info("  ✓ cells_face indices valid: max_index=%d < num_faces=%d", max_face_idx, self.num_face)
        
        # Check boundary conditions
        from Utils.utilities import NodeType
        boundary_counts = {}
        for t in torch.unique(self.faces_type):
            count = torch.sum(self.faces_type == t).item()
            try:
                boundary_name = NodeType(t.item()).name
            except:
                boundary_name = f"Type {t.item()}"
            boundary_counts[boundary_name] = count
            
        logging.info("  Boundary condition counts:")
        for name, count in boundary_counts.items():
            logging.info("    %s: %d", name, count)
        
        # Check normal vectors
        normal_lengths = torch.norm(self.cells_face_normal, dim=1)
        logging.info("  Face normal statistics:")
        logging.info("    Min length: %.6f", torch.min(normal_lengths).item())
        logging.info("    Max length: %.6f", torch.max(normal_lengths).item())
        logging.info("    Mean length: %.6f", torch.mean(normal_lengths).item())
        logging.info("    Std deviation: %.6f", torch.std(normal_lengths).item())
        
        # Check face areas
        logging.info("  Face area statistics:")
        logging.info("    Min area: %.6f", torch.min(self.faces_areas).item())
        logging.info("    Max area: %.6f", torch.max(self.faces_areas).item())
        logging.info("    Mean area: %.6f", torch.mean(self.faces_areas).item())
        logging.info("    Total surface area: %.6f", torch.sum(self.faces_areas).item())

    def _parse_boundary_conditions(self):
        """Parse boundary conditions from field files (0/U, 0/p, etc.)"""
        import re
        
        self.boundary_conditions = {}
        
        # List of field files to check
        field_files = ['U', 'p']  # Can be extended for other fields
        
        for field_name in field_files:
            field_path = os.path.join(self.case_path, '0', field_name)
            if not os.path.exists(field_path):
                logging.warning("Warning: Field file %s not found", field_path)
                continue
                
            try:
                with open(field_path, 'r') as f:
                    content = f.read()
                
                # Find boundaryField section
                boundary_start = content.find('boundaryField')
                if boundary_start == -1:
                    logging.warning("Warning: No boundaryField section found in %s", field_path)
                    continue
                
                # Extract the boundaryField section
                brace_count = 0
                start_idx = content.find('{', boundary_start)
                current_idx = start_idx
                
                while current_idx < len(content):
                    if content[current_idx] == '{':
                        brace_count += 1
                    elif content[current_idx] == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            break
                    current_idx += 1
                
                boundary_section = content[start_idx:current_idx+1]
                
                # Parse each boundary patch
                patch_pattern = r'(\w+)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}'
                patches = re.findall(patch_pattern, boundary_section)
                
                if field_name not in self.boundary_conditions:
                    self.boundary_conditions[field_name] = {}
                
                for patch_name, patch_content in patches:
                    # Extract type
                    type_match = re.search(r'type\s+(\w+);', patch_content)
                    bc_type = type_match.group(1) if type_match else 'unknown'
                    
                    # Extract value if present
                    value_match = re.search(r'value\s+([^;]+);', patch_content)
                    value = value_match.group(1).strip() if value_match else None
                    
                    self.boundary_conditions[field_name][patch_name] = {
                        'type': bc_type,
                        'value': value
                    }
                    
            except Exception as e:
                logging.error("Error parsing boundary conditions from %s: %s", field_path, e)
        
        logging.info("Parsed boundary conditions:")
        for field, patches in self.boundary_conditions.items():
            logging.info("  %s:", field)
            for patch, bc in patches.items():
                logging.info("    %s: type=%s, value=%s", patch, bc['type'], bc.get('value', 'N/A'))

    def _map_openfoam_bc_to_nodetype(self, patch_name, u_bc_type, p_bc_type, geom_type=None):
        """Map OpenFOAM boundary condition types to simplified NodeType enum"""

        # Handle cyclic boundaries first (highest priority after empty)
        if geom_type == 'cyclic':
            return NodeType.CYCLIC

        # Handle empty boundaries (highest priority)
        if u_bc_type == 'empty' or p_bc_type == 'empty':
            return NodeType.EMPTY

        # Handle velocity boundary conditions
        if u_bc_type == 'fixedValue':
            # fixedValue for velocity -> INFLOW
            return NodeType.INFLOW
        elif u_bc_type in ['noSlip', 'slip'] or 'wall' in u_bc_type.lower():
            # noSlip, slip, or any wall type -> WALL_BOUNDARY
            return NodeType.WALL_BOUNDARY
        elif u_bc_type == 'zeroGradient':
            # zeroGradient for velocity (usually outlet) -> OUTFLOW
            return NodeType.OUTFLOW
        elif p_bc_type == 'zeroGradient' and u_bc_type == 'unknown':
            # Only pressure has zeroGradient -> likely wall for pressure
            return NodeType.ZERO_GRADIENT
            
        # If we can't determine from U field, try using patch name heuristics
        patch_lower = patch_name.lower()
        if 'inlet' in patch_lower or 'inflow' in patch_lower:
            return NodeType.INFLOW
        elif 'outlet' in patch_lower or 'outflow' in patch_lower:
            return NodeType.OUTFLOW
        elif 'wall' in patch_lower:
            return NodeType.WALL_BOUNDARY
            
        # For any unrecognized boundary condition, default to EMPTY and raise error
        error_msg = f"Unrecognized boundary condition: patch={patch_name}, U_bc={u_bc_type}, p_bc={p_bc_type}. Defaulting to EMPTY."
        logging.warning(error_msg)
        
        # Default to EMPTY for unrecognized boundary conditions
        raise ValueError(error_msg)
        
    def _get_boundary_type_name(self, face_type_int):
        """Convert face type integer to readable boundary name.
        
        This method dynamically imports the NodeType enum from utilities 
        and provides a mapping from face type integers to readable names.
        """
        try:
            # Dynamically import NodeType to handle potential enum changes
            from Utils.utilities import NodeType
            
            # Convert integer to NodeType enum and get name
            if face_type_int in [node_type.value for node_type in NodeType]:
                node_type = NodeType(face_type_int)
                return node_type.name.lower()
            else:
                return f"unknown_{face_type_int}"
                
        except Exception as e:
            logging.warning("Warning: Could not convert face type %d to name: %s", face_type_int, e)
            return f"type_{face_type_int}"
        
    def _export_boundary_vtu_files(self, output_h5_path=None):
        """Export VTU files for boundary face visualization with face type coloring.
        
        This method creates separate VTU files for each boundary type to verify
        that face type conversion is working correctly.
        
        Args:
            output_h5_path: Path to the H5 file, VTU files will be saved in the same directory
        """
        logging.info("\nExporting VTU files for boundary face verification...")

        # Import necessary modules
        from Post_process.to_vtk import write_hybrid_mesh_to_vtu
        import os
        import traceback

        # Create VTU output directory - use H5 file directory if provided
        if output_h5_path:
            vtu_output_dir = os.path.join(os.path.dirname(output_h5_path), "vtu_boundary_faces")
        else:
            # Fallback for direct calls to this method
            vtu_output_dir = os.path.join(self.case_path, "h5", "vtu_boundary_faces")
        os.makedirs(vtu_output_dir, exist_ok=True)

        unique_face_types = torch.unique(self.faces_type.squeeze())
        logging.info("  Found %d unique face types to export", len(unique_face_types))

        # Get basic data
        mesh_pos = self.mesh_pos
        face_node = self.face_node.long().squeeze()
        faces_edge = self.faces_edge.long().squeeze()
        faces_edge_ptr = self.faces_edge_ptr.long().squeeze()  # 面的情况下，node数量与edge数量相同，因此可以公用ptr

        for i_type in unique_face_types:
            # Get boundary type name
            boundary_type_name = self._get_boundary_type_name(i_type.item())
            
            try:
                logging.info("    Processing %s (type %d)...", boundary_type_name, i_type.item())
                
                # Create mask for current face type
                cur_type_mask = (self.faces_type.squeeze() == i_type)
                
                # Get faces of current type using vectorized operations
                cur_face_indices = torch.where(cur_type_mask)[0]
                
                if len(cur_face_indices) == 0:
                    logging.info("      No faces found for type %s", boundary_type_name)
                    continue
                
                # Get node and edge indices for faces of current type
                cur_face_node_mask = torch.isin(faces_edge_ptr, cur_face_indices)
                cur_face_node = face_node[cur_face_node_mask]
                cur_faces_edge = faces_edge[cur_face_node_mask]
                cur_faces_edge_ptr_raw = faces_edge_ptr[cur_face_node_mask]
                
                # Vectorized face index remapping (instead of for loop)
                # Create a mapping from global face indices to local indices
                max_face_idx = torch.max(cur_face_indices).item() + 1
                face_global_to_local = torch.full((max_face_idx,), -1, dtype=torch.long)
                face_global_to_local[cur_face_indices] = torch.arange(len(cur_face_indices), dtype=torch.long)
                
                # Apply the mapping to get local face indices
                ''' face_edge_ptr is equal to face_edge_node_ptr, so we can use the same mapping '''
                cur_faces_edge_ptr = face_global_to_local[cur_faces_edge_ptr_raw]
                
                # Get unique nodes and edges for local mapping
                unique_nodes, node_inverse_indices = torch.unique(cur_face_node, return_inverse=True)
                unique_edges, edge_inverse_indices = torch.unique(cur_faces_edge, return_inverse=True)
                
                # Extract positions for unique nodes and create local edge mappings
                cur_nodes_pos = mesh_pos[unique_nodes]
                
                # Remap face_node and faces_edge indices from global to local
                cur_face_node_local = node_inverse_indices
                cur_faces_edge_local = edge_inverse_indices
                
                # Create saving path
                case_name = os.path.basename(self.case_path)
                saving_path = os.path.join(vtu_output_dir, f"{case_name}_{boundary_type_name}")
                
                # Data location
                datalocation = "face"
                
                # Create face type data for current faces only
                faces_type_data = torch.full((len(cur_face_indices), 1), i_type.item(), dtype=torch.long)
                
                # Extract face normals for current face type (use pre-computed normals)
                cur_faces_normals = self.faces_normals[cur_face_indices]  # [n_faces, 3]
                
                # Export VTU file with both faces_type and faces_normal data
                write_hybrid_mesh_to_vtu(
                    mesh_pos=cur_nodes_pos.cpu().numpy(), 
                    data_dict={
                        f"{datalocation}|faces_type": faces_type_data.cpu().numpy(),
                        f"{datalocation}|faces_normal": cur_faces_normals.cpu().numpy(),
                    }, 
                    cells_node=cur_face_node_local.cpu(), 
                    cells_ptr=cur_faces_edge_ptr.cpu(),
                    file_path=f"{saving_path}.vtu",
                )
                
                logging.info("      Exported %d faces to %s.vtu", len(cur_face_indices), saving_path)
                
            except Exception as e:
                logging.warning("  VTU export failed for %s: %s", boundary_type_name, e)
                logging.info("  Continuing with mesh processing...")
                traceback.print_exc()
        
        logging.info("  VTU files exported to: %s", vtu_output_dir)

    def _export_full_mesh_vtu(self, output_h5_path=None):
        """Export VTU file for the complete mesh using PyVista cell format."""
        logging.info("\nExporting VTU file for complete mesh...")

        try:
            # Create VTU output directory - use H5 file directory if provided
            if output_h5_path:
                vtu_output_dir = os.path.join(os.path.dirname(output_h5_path), "vtu_boundary_faces")
            else:
                # Fallback for direct calls to this method
                vtu_output_dir = os.path.join(self.case_path, "h5", "vtu_boundary_faces")
            os.makedirs(vtu_output_dir, exist_ok=True)

            # Create saving path for full mesh
            case_name = os.path.basename(self.case_path)
            saving_path = os.path.join(vtu_output_dir, f"{case_name}_full_mesh.vtu")

            data_dict = {
                'cell|cells_volume': self.cells_volume if hasattr(self, 'cells_volume') else None,
                'cell|cells_pos': self.cells_pos if hasattr(self, 'cells_pos') else None,
            }

            export_full_mesh_vtu(
                mesh_pos=self.mesh_pos,
                pv_cells_node=self.pv_cells_node,
                pv_cells_type=self.pv_cells_type,
                save_file_path=saving_path,
                data_dict=data_dict
            )
        except Exception as e:
            import traceback
            logging.warning("  Full mesh VTU export failed: %s", e)
            logging.info("  Continuing with mesh processing...")
            traceback.print_exc()
        
    def _calculate_cells_volume(self):
        """
        Calculate cell volumes using the divergence theorem with vectorized operations.
        
        For the linear function f(x) = x, we have div(f) = 3 in 3D.
        By the divergence theorem: ∫∫∫ div(f) dV = ∫∫ f·n dS
        Therefore: Volume = (1/3) * ∫∫ f·n dS = (1/3) * ∑ face_center · (normal * area)
        """
        logging.info("  Calculating cell volumes using divergence theorem...")
        
        # Get face centers for all cell-face pairs
        faces_centers_expanded = self.faces_pos[self.cells_face]  # [total_cell_faces, 3]
        
        # Linear function: f(x) = x at each face center
        linear_function_values = faces_centers_expanded  # [total_cell_faces, 3]
        
        # Surface vectors: normal * area for each cell-face pair
        surface_vectors = self.cells_face_normal * self.cells_face_area  # [total_cell_faces, 3]
        
        # Calculate f·n for each face: dot product of linear function and surface vector
        face_flux = torch.sum(linear_function_values * surface_vectors, dim=1, keepdim=True)  # [total_cell_faces, 1]
        
        # Sum face fluxes for each cell using scatter
        volume_integral = scatter(
            src=face_flux,
            index=self.cells_face_ptr,
            dim=0,
            reduce='sum'
        )  # [num_cells, 1]
        
        # Apply divergence theorem: Volume = (1/div(f)) * ∫ f·n dS
        # For f(x) = x in 3D, div(f) = 3, so Volume = (1/3) * integral
        self.cells_volume = (1.0 / 3.0) * volume_integral  # [num_cells, 1]
        
        # Validate volumes (should all be positive)
        min_volume = torch.min(self.cells_volume).item()
        max_volume = torch.max(self.cells_volume).item()
        mean_volume = torch.mean(self.cells_volume).item()
        total_volume = torch.sum(self.cells_volume).item()
        
        logging.info("    Cell volume statistics:")
        logging.info("    Min volume: %.8f", min_volume)
        logging.info("    Max volume: %.8f", max_volume)
        logging.info("    Mean volume: %.8f", mean_volume)
        logging.info("    Total domain volume: %.8f", total_volume)
        
        # Check for negative volumes (indicates mesh issues)
        negative_volumes = torch.sum(self.cells_volume < 0).item()
        if negative_volumes > 0:
            logging.warning("    %d cells have negative volumes!", negative_volumes)
        else:
            logging.info("    ✓ All cell volumes are positive")
            
    def _calculate_edge_node_x(self):
        """Calculate edge_node_x using existing domain separation logic"""
        domain_list = seperate_domain(
            cells_ptr=self.cells_node_ptr,
            cells_node=self.cells_node
        )
        
        edge_node_x = []
        for domain in domain_list:
            _ct, _cells_node, _cells_face, _cells_index, _ = domain
            edge_node_x.append(
                compose_edge_node_x(cells_type=_ct, cells_node=_cells_node)
            )
        
        edge_node_x = torch.cat(edge_node_x, dim=1)
        self.edge_node_x = torch.unique(edge_node_x[:,~(edge_node_x[0]==edge_node_x[1])], dim=1)
        
    def save_to_hdf5(self, output_path, case_name):
        """
        Saves mesh data to HDF5 format using the new naming convention.
        
        Converts torch tensors to numpy arrays and saves them as HDF5 datasets
        with structured naming: node|*, face|*, cell|*, cpd|* for compound attributes.
        
        Args:
            output_path: Path where the HDF5 file will be saved
        """
        with h5py.File(output_path, 'w') as hf:
            # Add metadata
            hf.attrs['case_name'] = case_name
            hf.attrs['dimension'] = '3D'
            hf.attrs['case_path'] = self.case_path
            hf.attrs['num_points'] = self.num_point
            hf.attrs['num_faces'] = self.num_face
            hf.attrs['num_cells'] = self.num_cell
            hf.attrs['num_internal_faces'] = self.num_inner_face
            
            # Geometry data
            hf.create_dataset('node|pos', data=self.mesh_pos.numpy())
            hf.create_dataset('face|face_center_pos', data=self.faces_pos.numpy())
            hf.create_dataset('cell|centroid', data=self.cells_pos.numpy())
            
            # Connectivity data
            hf.create_dataset('cells_node', data=self.cells_node.reshape(-1).numpy())
            hf.create_dataset('cells_node_ptr', data=self.cells_node_ptr.reshape(-1).numpy())
            hf.create_dataset('cells_face', data=self.cells_face.reshape(-1).numpy())
            hf.create_dataset('cells_face_ptr', data=self.cells_face_ptr.reshape(-1).numpy())
            hf.create_dataset('face_node', data=self.face_node.numpy())
            hf.create_dataset('face_node_ptr', data=self.face_node_ptr.numpy())
            
            # Edge connectivity
            hf.create_dataset('edge_node', data=self.edge_node.numpy())
            hf.create_dataset('face_edge', data=self.faces_edge.numpy())
            hf.create_dataset('face_edge_ptr', data=self.faces_edge_ptr.numpy())
            hf.create_dataset('face|neighbor_cell', data=self.faces_neighbor_cell.numpy())
            hf.create_dataset('face|face_type', data=self.faces_type.numpy())
            hf.create_dataset('edge_node_x', data=self.edge_node_x.numpy())

            # Geometric attributes
            hf.create_dataset('face|face_area', data=self.faces_areas.numpy().reshape(-1))
            hf.create_dataset('cells_face_area', data=self.cells_face_area.numpy())
            hf.create_dataset('cells_face_normal', data=self.cells_face_normal.numpy())
            hf.create_dataset('cell|cells_volume', data=self.cells_volume.numpy())
            
            # Compound attributes (boundary faces treated as virtual cells)
            hf.create_dataset('cpd|cell_type', data=self.cpd_cell_type.numpy().reshape(-1))
            hf.create_dataset('cpd|neighbor_cell', data=self.cpd_neighbor_cell.numpy())
            hf.create_dataset('cpd|centroid', data=self.cpd_centroid.numpy())

            # Cyclic boundary condition data
            hf.create_dataset('cyclic|neighbor_cell', data=self.cyclic_neighbor_cell.numpy())

            # PyVista format data for VTU export
            hf.create_dataset('pv_cells_node', data=self.pv_cells_node.numpy())
            hf.create_dataset('pv_cells_type', data=self.pv_cells_type.numpy())
            
        logging.info("Successfully converted OpenFOAM mesh to HDF5: %s", output_path)
        
if __name__ == "__main__":
    # --- Configuration ---
    # Path to the OpenFOAM case directory relative to this script's file location.
    # The default case is a 3D lid-driven cavity example.
    # You can change this to your own case directory.
    case_path = f'{os.getcwd()}/mesh_example/Cylinder_flow_3D-simple'
    case_name = os.path.basename(case_path) + '.h5'
    plt_vis = True
    # --- End Configuration ---

    # Construct absolute paths
    script_dir = os.path.dirname(os.path.abspath(__file__))
    case_dir_path = os.path.normpath(os.path.join(script_dir, case_path))

    # Set up logging to file and console
    log_dir = os.path.join(case_dir_path, 'logs') # Create a logs directory
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'openfoam_parser.log')
    
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, mode='w'), # Overwrite log file each run
            logging.StreamHandler(sys.stdout) # Also log to console
        ]
    )
    
    logging.info("Starting OpenFOAM parser script")

    # Save output to a 'h5' subdirectory within the case folder
    output_dir = os.path.join(case_dir_path, 'h5')
    os.makedirs(output_dir, exist_ok=True)
    output_hdf5_path = os.path.join(output_dir, case_name)
    
    logging.info("Processing OpenFOAM case from: %s", case_dir_path)
    
    try:
        # Create and process the OpenFOAM mesh
        logging.info("Creating OpenFOAM_Manager")
        manager = OpenFOAM_Manager(case_dir_path, plt_vis=plt_vis)
        
        # Print some debug information
        logging.info("\nMesh statistics:")
        logging.info("- Number of points: %d", manager.num_point)
        logging.info("- Number of faces: %d", manager.num_face)
        logging.info("- Number of cells: %d", manager.num_cell)
        logging.info("- Number of internal faces: %d", manager.num_inner_face)
        logging.info("- Number of edges: %d", len(manager.edge_node))
        logging.info("- Number of boundary patches: %d", len(manager.boundary_raw))
        
        # List boundary patches and their types
        logging.info("\nBoundary patches:")
        
        for name, props in manager.boundary_raw.items():
            if isinstance(props, dict):
                patch_type = props['type']
                face_count = props['nFaces']
            else:
                patch_type = props.type if hasattr(props, 'type') else "unknown"
                face_count = props.num if hasattr(props, 'num') else 0
                
            if isinstance(patch_type, bytes):
                patch_type = patch_type.decode('utf-8')
                
            logging.info("- %s: type=%s, faces=%d", name, patch_type, face_count)
        
        # Save to HDF5
        logging.info("Saving to HDF5")
        manager.save_to_hdf5(output_hdf5_path, case_name)
        
        # Export VTU files for boundary face visualization
        logging.info("Exporting VTU files for boundary visualization")
        manager._export_boundary_vtu_files(output_h5_path=output_hdf5_path)
        
        # Export VTU file for the complete mesh
        logging.info("Exporting VTU file for complete mesh")
        manager._export_full_mesh_vtu(output_h5_path=output_hdf5_path)
        
        logging.info("\nConversion completed successfully!")
        
    except Exception as e:
        logging.error("Error processing OpenFOAM case: %s", str(e), exc_info=True)
        import traceback
        traceback.print_exc()

# ==============================================================================
# HDF5 Data Shape Documentation
# ==============================================================================
#
# This section documents the shape of each dataset saved to the HDF5 file.
# Dataset names use the new HDF5 naming convention implemented in save_to_hdf5().
#
# Naming Conventions:
# - N_points: Total number of unique nodes (vertices) in the mesh.
# - N_faces: Total number of unique faces in the mesh.
# - N_cells: Total number of unique cells (polyhedra) in the mesh.
# - N_edges: Total number of unique edges in the mesh.
# - N_total_cell_nodes: Sum of the number of nodes for each cell.
# - N_total_cell_faces: Sum of the number of faces for each cell.
# - N_total_face_nodes: Sum of the number of nodes for each face.
# - N_total_face_edges: Sum of the number of edges for each face.
#
# ------------------------------------------------------------------------------
# HDF5 Dataset Name      | Shape                     | Description
# ------------------------------------------------------------------------------
#
# === Geometry Data ===
# node|pos               | (N_points, 3)             | XYZ coordinates for each node.
# face|face_center_pos   | (N_faces, 3)              | XYZ coordinates for the center of each face.
# cell|centroid          | (N_cells, 3)              | XYZ coordinates for the center of each cell.
#
# === Connectivity Data (Pointer-based) ===
# cells_node             | (N_total_cell_nodes, 1)   | Node indices belonging to cells.
# cells_node_ptr         | (N_total_cell_nodes, 1)   | Cell index for each entry in cells_node.
#
# cells_face             | (N_total_cell_faces, 1)   | Face indices belonging to cells.
# cells_face_ptr         | (N_total_cell_faces, 1)   | Cell index for each entry in cells_face.
#
# face_node              | (N_total_face_nodes, 1)   | Node indices belonging to faces.
# face_node_ptr          | (N_total_face_nodes, 1)   | Face index for each entry in face_node.
#
# face_edge              | (N_total_face_edges, 1)   | Edge indices belonging to faces.
# face_edge_ptr          | (N_total_face_edges, 1)   | Face index for each entry in face_edge.
#
# === Connectivity Data (Direct Mapping) ===
# edge_node              | (N_edges, 2)              | Node indices for each unique edge.
# face|neighbor_cell     | (N_faces, 2)              | Cell indices for the two cells neighboring each face.
#                                                     | For boundary faces, both indices are the same.
#
# === Attribute Data ===
# face|face_type         | (N_faces, 1)              | Integer code for the type of each face (e.g., boundary, internal).
# face|face_area         | (N_faces, 1)              | Area of each face.
# cells_volume          | (N_cells, 1)              | Volume of each cell.
#
# === Cell-Face Pair Data ===
# cells_face_area       | (N_total_cell_faces, 1)   | Area of each face, repeated for each cell it belongs to.
# cells_face_normal     | (N_total_cell_faces, 3)   | Normal vector of each face, oriented outwards from its cell.
#
# === Compound Attributes (cpd|*) ===
# These attributes combine original cells with boundary faces treated as virtual cells
# N_compound_cells = N_cells + N_boundary_faces
#
# cpd|cell_type          | (N_compound_cells, 1)     | Type of each compound cell: NORMAL for original cells,
#                                                     | boundary condition type for boundary face centers.
# cpd|neighbor_cell      | (2, N_faces)              | Modified neighbor cell indices where boundary faces
#                                                     | point to their corresponding virtual boundary cells.
# cpd|centroid           | (N_compound_cells, 3)     | Coordinates combining original cell centers and
#                                                     | boundary face centers as virtual cells.
#
# ==============================================================================
