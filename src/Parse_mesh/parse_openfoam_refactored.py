"""
Refactored OpenFOAM mesh parser with improved efficiency and object-oriented design.

This module provides an optimized OpenFOAM mesh parser that inherits from BaseMeshManager
and implements efficient boundary processing with merged loops and separated concerns.
"""

import sys
import os
import logging
import re
from typing import Dict, List, Tuple, Optional, Any

file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)

import openfoamparser as Ofpp
import numpy as np
import torch
import h5py
from torch_scatter import scatter
import pyvista as pv

from Utils.utilities import NodeType
from Parse_mesh.parse_base import BaseMeshManager
from Parse_mesh.parse_to_h5 import seperate_domain, compose_edge_node_x
from Post_process.to_vtk import export_full_mesh_vtu, write_hybrid_mesh_to_vtu
from Parse_mesh.parse_to_h5 import build_k_hop_edge_index


class OpenFOAMManager(BaseMeshManager):
    """
    Optimized OpenFOAM mesh manager with unified boundary processing and improved efficiency.
    
    This class inherits from BaseMeshManager and implements OpenFOAM-specific mesh processing
    with the following optimizations:
    - Unified boundary processing in single loop
    - Merged connectivity calculations
    - Separated visualization concerns
    - Vectorized geometric calculations
    """
    
    def __init__(self, case_path: str, plt_vis: bool = False,
                 enable_vertex_order_validation: bool = False,):
        """
        Initialize OpenFOAM mesh manager.

        Args:
            case_path: Path to OpenFOAM case directory
            plt_vis: Whether to enable visualization
            enable_vertex_order_validation: Enable strict face vertex order validation
        """
        super().__init__()

        self.case_path = case_path
        self.plt_vis = plt_vis

        # Store validation parameters
        self.enable_vertex_order_validation = enable_vertex_order_validation

        # Load OpenFOAM mesh
        mesh = Ofpp.FoamMesh(self.case_path)
        
        # Extract mesh properties
        self.num_point = mesh.num_point
        self.num_face = mesh.num_face
        self.num_cell = mesh.num_cell + 1  # Fix off-by-one bug
        self.num_inner_face = mesh.num_inner_face
        
        # Raw data arrays
        self.points_raw = torch.from_numpy(np.asarray(mesh.points)).float()
        self.faces_raw = mesh.faces
        self.owner_raw = torch.from_numpy(np.asarray(mesh.owner)).long()
        self.neighbour_raw = torch.from_numpy(np.asarray(mesh.neighbour)).long()
        self.boundary_raw = mesh.boundary
        self.cell_faces_raw = mesh.cell_faces
        
        # Additional OpenFOAM-specific attributes
        self.edge_node = None
        self.edge_node_x = None
        self.faces_edge = None
        self.faces_edge_ptr = None
        self.cells_face_area = None
        self.cells_face_normal = None
        self.cyclic_neighbor_cell = None
        self.boundary_conditions = {}
        
        # Process mesh data
        logging.info("Processing OpenFOAM mesh data with optimized pipeline...")
        self._process_mesh_pipeline()
        
    def _process_mesh_pipeline(self):
        """Execute the complete mesh processing pipeline"""
        # Step 1: Extract base mesh data and connectivity in unified manner
        self._extract_base_mesh_data()

        # Step 2: Calculate geometric properties
        self._calculate_derived_geometric_data()
        
        # Step 3: Process connectivity data with merged loops
        self._calculate_connectivity_data()
        
        # Step 4: Unified boundary processing (replaces multiple separate methods)
        self._process_boundaries_unified()
        
        # Step 5: Calculate geometric properties (normals, areas, volumes)
        self._calculate_geometric_properties()
        
        # Step 6: Calculate compound attributes
        self._calculate_compound_attributes()
        
        # Step 7: Validate mesh
        self._validate_mesh_consistency()

        # Step 8: Validate face areas and normals statistics
        self._validate_face_statistics()

        # Step 9: Create visualizations if requested
        if self.plt_vis:
            self.create_visualizations(self.case_path+'/h5', os.path.basename(self.case_path), enable_matplotlib=False)
    
    def _extract_base_mesh_data(self):
        """Extract base mesh data with optimized edge processing"""
        # Set node positions
        self.mesh_pos = self.points_raw
        
        # Process faces and edges in unified loop
        face_node_list = []
        face_node_ptr_list = []
        edge_dict = {}
        edge_node_list = []
        faces_edge_list = []
        faces_edge_ptr_list = []
        
        for face_idx, face in enumerate(self.faces_raw):
            # Process face nodes
            face_node_list.extend(face)
            face_node_ptr_list.extend([face_idx] * len(face))
            
            # Process edges for this face
            current_face_edges = []
            num_nodes = len(face)
            
            for i in range(num_nodes):
                n1, n2 = face[i], face[(i+1) % num_nodes]
                edge = tuple(sorted([int(n1), int(n2)]))
                
                if edge not in edge_dict:
                    edge_idx = len(edge_node_list)
                    edge_dict[edge] = edge_idx
                    edge_node_list.append(edge)
                else:
                    edge_idx = edge_dict[edge]
                
                current_face_edges.append(edge_idx)
                faces_edge_ptr_list.append(face_idx)
            
            faces_edge_list.extend(current_face_edges)
        
        # Convert to tensors
        self.face_node = torch.tensor(face_node_list, dtype=torch.long).squeeze()
        self.face_node_ptr = torch.tensor(face_node_ptr_list, dtype=torch.long).squeeze()
        self.edge_node = torch.tensor(edge_node_list, dtype=torch.long).squeeze().T # [2, E]
        self.faces_edge = torch.tensor(faces_edge_list, dtype=torch.long).squeeze()
        self.faces_edge_ptr = torch.tensor(faces_edge_ptr_list, dtype=torch.long).squeeze()
    
    def _calculate_derived_geometric_data(self):
        """Calculate face centers using vectorized operations"""
        self.faces_pos = scatter(
            src=self.mesh_pos[self.face_node.squeeze()], 
            index=self.face_node_ptr.squeeze(),
            dim=0, 
            reduce='mean'
        )
    
    def _calculate_connectivity_data(self):
        """Calculate connectivity data with merged cell processing"""
        # Process cells_face data
        cells_face_list = []
        cells_face_ptr_list = []
        
        # Merged processing: cells_node, pv_cells_node, and cells_pos in single loop
        cells_node_list = []
        cells_node_ptr_list = []
        pv_cells_node_list = []
        pv_cells_type_list = []
        
        for cell_idx in range(self.num_cell):
            cell_face_indices = self.cell_faces_raw[cell_idx]
            
            # Process cells_face
            cells_face_list.extend(cell_face_indices)
            cells_face_ptr_list.extend([cell_idx] * len(cell_face_indices))
            
            # Collect unique nodes from all faces
            nodes_set = set()
            for face_idx in cell_face_indices:
                face_nodes = self.faces_raw[face_idx]
                nodes_set.update(face_nodes)
            
            # Process cells_node
            cell_nodes = list(nodes_set)
            cells_node_list.extend(cell_nodes)
            cells_node_ptr_list.extend([cell_idx] * len(cell_nodes))
            
            # Process PyVista format in same loop
            pv_cells_type_list.append(pv.CellType.POLYHEDRON)
            face_connectivity = [len(cell_face_indices)]
            
            for face_idx in cell_face_indices:
                face_nodes = self.faces_raw[face_idx]
                face_connectivity.append(len(face_nodes))
                face_connectivity.extend(face_nodes)
            
            pv_cells_node_list.extend([len(face_connectivity)] + face_connectivity)
        
        # Convert to tensors
        self.cells_face = torch.tensor(cells_face_list, dtype=torch.long)
        self.cells_face_ptr = torch.tensor(cells_face_ptr_list, dtype=torch.long)
        self.cells_node = torch.tensor(cells_node_list, dtype=torch.long)
        self.cells_node_ptr = torch.tensor(cells_node_ptr_list, dtype=torch.long)
        self.pv_cells_node = torch.tensor(pv_cells_node_list, dtype=torch.long)
        self.pv_cells_type = torch.tensor(pv_cells_type_list, dtype=torch.long)
        
        # Calculate cell centers
        self.cells_pos = scatter(
            src=self.mesh_pos[self.cells_node], 
            index=self.cells_node_ptr,
            dim=0, 
            reduce='mean'
        )
        
        # Calculate edge_node_x
        self._calculate_edge_node_x()
        
    def _process_boundaries_unified(self):
        """
        Unified boundary processing that handles all boundary-related calculations in a single pass.
        
        This replaces the original separate methods:
        - _calculate_faces_neighbor_cell
        - _determine_face_types  
        - _extract_cyclic_neighbor_relationships
        """
        logging.info("Processing boundaries with unified approach...")
        
        # Parse boundary conditions from field files
        self._parse_boundary_conditions()

        # Parse detailed boundary information from boundary file
        detailed_boundary_info = self._parse_boundary_file()

        # Use base class unified boundary processing with detailed info
        boundary_info = self._process_boundary_data_unified(self.boundary_raw, self.boundary_conditions, detailed_boundary_info)
        
        # Initialize face neighbor relationships
        num_total_faces = len(self.faces_raw)
        self.faces_neighbor_cell = torch.zeros((num_total_faces, 2), dtype=torch.long)
        self.faces_neighbor_cell[:, 0] = self.owner_raw
        
        # Initialize face types (all internal faces are NORMAL)
        self.faces_type = torch.full((num_total_faces, 1), NodeType.NORMAL, dtype=torch.long)
        
        # Process internal faces
        internal_faces = set(range(num_total_faces)) - boundary_info['boundary_faces']
        internal_face_indices = list(internal_faces)
        
        if len(internal_face_indices) == len(self.neighbour_raw):
            internal_tensor = torch.tensor(internal_face_indices, dtype=torch.long)
            self.faces_neighbor_cell[internal_tensor, 1] = self.neighbour_raw
        else:
            self.faces_neighbor_cell[:len(self.neighbour_raw), 1] = self.neighbour_raw
        
        # Process boundary faces and face types
        for face_idx, node_type in boundary_info['face_types'].items():
            self.faces_type[face_idx] = node_type
        
        # Set boundary face neighbors (duplicate owner)
        boundary_tensor = torch.tensor(list(boundary_info['boundary_faces']), dtype=torch.long)
        self.faces_neighbor_cell[boundary_tensor, 1] = self.faces_neighbor_cell[boundary_tensor, 0]
        
        # Process cyclic boundaries
        self._process_cyclic_boundaries(boundary_info['cyclic_patches'])
        
        # 开始将face_type转换为node_type, 遵循以下优先级：WALL>INFLOW=OUTFLOW>CYCLIC
        self.node_type = torch.full((self.mesh_pos.shape[0],1),NodeType.NORMAL)
        self.face_type_expand = self.faces_type[self.face_node_ptr]
        
        mask_cyclic = (self.face_type_expand == NodeType.CYCLIC).squeeze()
        mask_inflow = (self.face_type_expand == NodeType.INFLOW).squeeze()
        mask_outflow = (self.face_type_expand == NodeType.OUTFLOW).squeeze()
        mask_wall = (self.face_type_expand == NodeType.WALL_BOUNDARY).squeeze()
        
        self.node_type[self.face_node[mask_cyclic]] = NodeType.CYCLIC
        self.node_type[self.face_node[mask_inflow]] = NodeType.INFLOW
        self.node_type[self.face_node[mask_outflow]] = NodeType.OUTFLOW
        self.node_type[self.face_node[mask_wall]] = NodeType.WALL_BOUNDARY
        self.node_type = self.node_type.squeeze()
        

    def _parse_boundary_conditions(self):
        """Parse boundary conditions from field files"""
        self.boundary_conditions = {}

        # Parse U field file
        u_file = os.path.join(self.case_path, '0', 'U')
        if os.path.exists(u_file):
            self.boundary_conditions['U'] = self._parse_field_file(u_file)

        # Parse p field file
        p_file = os.path.join(self.case_path, '0', 'p')
        if os.path.exists(p_file):
            self.boundary_conditions['p'] = self._parse_field_file(p_file)

        # Log parsed boundary conditions
        logging.info("Parsed boundary conditions:")
        for field_name, field_bcs in self.boundary_conditions.items():
            logging.info("  %s:", field_name)
            for patch_name, bc_data in field_bcs.items():
                bc_type = bc_data.get('type', 'unknown')
                bc_value = bc_data.get('value', None)
                logging.info("    %s: type=%s, value=%s", patch_name, bc_type, bc_value)

    def _parse_field_file(self, field_file_path: str) -> Dict:
        """Parse OpenFOAM field file to extract boundary conditions"""
        boundary_conditions = {}

        try:
            with open(field_file_path, 'r') as f:
                content = f.read()

            # Find boundaryField section
            boundary_field_match = re.search(r'boundaryField\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}', content, re.MULTILINE | re.DOTALL)
            if not boundary_field_match:
                return boundary_conditions

            boundary_content = boundary_field_match.group(1)

            # Extract each patch's boundary condition
            patch_pattern = r'(\w+)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}'
            patch_matches = re.finditer(patch_pattern, boundary_content)

            for patch_match in patch_matches:
                patch_name = patch_match.group(1)
                patch_content = patch_match.group(2)

                # Extract type
                type_match = re.search(r'type\s+(\w+)\s*;', patch_content)
                bc_type = type_match.group(1) if type_match else 'unknown'

                # Extract value (if present)
                value_match = re.search(r'value\s+([^;]+);', patch_content)
                bc_value = value_match.group(1).strip() if value_match else None

                boundary_conditions[patch_name] = {
                    'type': bc_type,
                    'value': bc_value
                }

        except Exception as e:
            logging.error("❌ Failed to parse boundary conditions from %s: %s", field_file_path, e)
            logging.error("   This may affect face type determination!")

        return boundary_conditions

    def _parse_boundary_file(self) -> Dict:
        """Parse the boundary file to get detailed boundary information including neighbourPatch"""
        boundary_file = os.path.join(self.case_path, 'constant', 'polyMesh', 'boundary')
        detailed_info = {}

        if not os.path.exists(boundary_file):
            logging.warning("Boundary file not found: %s", boundary_file)
            return detailed_info

        try:
            with open(boundary_file, 'r') as f:
                content = f.read()

            # Simple parsing of OpenFOAM boundary file
            # Look for boundary definitions
            import re

            # Find all boundary patch definitions
            patch_pattern = r'(\w+)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}'
            patches = re.findall(patch_pattern, content)

            for patch_name, patch_content in patches:
                if patch_name in ['FoamFile', 'boundaryField']:  # Skip header sections
                    continue

                patch_info = {}

                # Extract key-value pairs
                kv_pattern = r'(\w+)\s+([^;]+);'
                kvs = re.findall(kv_pattern, patch_content)

                for key, value in kvs:
                    value = value.strip()
                    # Remove quotes if present
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    patch_info[key] = value

                detailed_info[patch_name] = patch_info

            logging.info("Parsed detailed boundary info for %d patches", len(detailed_info))
            for patch_name, info in detailed_info.items():
                if 'neighbourPatch' in info:
                    logging.info("  %s: type=%s, neighbourPatch=%s",
                               patch_name, info.get('type', 'unknown'), info['neighbourPatch'])
                else:
                    logging.info("  %s: type=%s", patch_name, info.get('type', 'unknown'))

        except Exception as e:
            logging.error("Failed to parse boundary file: %s", e)

        return detailed_info

    def _process_cyclic_boundaries(self, cyclic_patches: Dict):
        """Process cyclic boundary relationships using explicit neighbourPatch information"""
        cyclic_connections = []
        processed_patches = set()

        # Convert patch names to strings for easier processing
        patch_list = [(name.decode('utf-8') if isinstance(name, bytes) else name, info)
                     for name, info in cyclic_patches.items()]

        for patch_name, patch_info in patch_list:
            if patch_name in processed_patches:
                continue

            # Get neighbour patch from explicit neighbourPatch field
            neighbour_name = patch_info.get('neighbour_patch')
            if not neighbour_name:
                logging.warning(f"Cyclic patch {patch_name} has no neighbourPatch defined")
                continue

            if isinstance(neighbour_name, bytes):
                neighbour_name = neighbour_name.decode('utf-8')

            # Find neighbour info
            neighbour_info = None
            for name, info in patch_list:
                if name == neighbour_name:
                    neighbour_info = info
                    break

            if not neighbour_info:
                logging.warning(f"Cannot find neighbour patch {neighbour_name} for cyclic patch {patch_name}")
                continue

            if patch_info['n_faces'] != neighbour_info['n_faces']:
                logging.warning(f"Cyclic patches {patch_name} and {neighbour_name} have different face counts: {patch_info['n_faces']} vs {neighbour_info['n_faces']}")
                continue

            # Create cyclic connections
            patch1_faces = torch.arange(patch_info['start_face'],
                                      patch_info['start_face'] + patch_info['n_faces'],
                                      dtype=torch.long)
            patch2_faces = torch.arange(neighbour_info['start_face'],
                                      neighbour_info['start_face'] + neighbour_info['n_faces'],
                                      dtype=torch.long)

            patch1_cells = self.faces_neighbor_cell[patch1_faces, 0]
            patch2_cells = self.faces_neighbor_cell[patch2_faces, 0]

            cyclic_connections.extend([
                torch.stack([patch1_cells, patch2_cells], dim=0),
                torch.stack([patch2_cells, patch1_cells], dim=0)
            ])

            processed_patches.add(patch_name)
            processed_patches.add(neighbour_name)

            logging.info(f"Created cyclic connection: {patch_name} <-> {neighbour_name} ({patch_info['n_faces']} faces each)")

        if cyclic_connections:
            self.cyclic_neighbor_cell = torch.cat(cyclic_connections, dim=1)
            logging.info(f"Total cyclic connections: {self.cyclic_neighbor_cell.shape[1]}")
        else:
            self.cyclic_neighbor_cell = torch.empty((2, 0), dtype=torch.long)
            logging.info("No cyclic connections found")
    
    def _calculate_edge_node_x(self):
        """Calculate edge_node_x using existing domain separation logic"""
        domain_list = seperate_domain(
            cells_ptr=self.cells_node_ptr,
            cells_node=self.cells_node
        )
        
        edge_node_x = []
        for domain in domain_list:
            _ct, _cells_node, _cells_face, _cells_index, _ = domain
            edge_node_x.append(
                compose_edge_node_x(cells_type=_ct, cells_node=_cells_node)
            )
        
        edge_node_x = torch.cat(edge_node_x, dim=1)
        self.edge_node_x = torch.unique(edge_node_x[:,~(edge_node_x[0]==edge_node_x[1])], dim=1)
        
        self.construct_stencil()
        
    def construct_stencil(
        self, 
        k_hop=2,
        BC_interal_neighbors=4,
        order=None,
    ):

        edge_node = self.edge_node.long()
        
        ''' 全局调用k-hop edge '''
        extra_edge_index = []
        for k in range(1, k_hop+1):
            extra_edge_index.append(build_k_hop_edge_index(torch.cat((edge_node,edge_node.flip(0)),dim=1),k=k))
        extra_edge_index = torch.cat((extra_edge_index), dim=1) # 此时刚出k-hop是包含所有内部边的且dual edge的
        # extra_edge_index = extra_edge_index[:,~(BC_mask[extra_edge_index[0]]&(BC_mask[extra_edge_index[1]]))] # 先排除边界指向边界的边ss
        extra_edge_index_unique = torch.unique(
            extra_edge_index[:,~(extra_edge_index[0]==extra_edge_index[1])].sort(0)[0],
            dim=1
        ) # 排除自环然后收缩为单向边

        self.edge_node_x = torch.cat((self.edge_node_x, extra_edge_index_unique), dim=1)
        ''' 全局调用k-hop edge '''
        
        ''' 检查模板并绘制'度'的分布 '''
        # # start plotting
        # support_edge = torch.cat((
        #     neighbor_cell_x, 
        #     internal_to_boundary
        # ), dim=1)
        # in_degree = pyg_utils.degree(support_edge[1], num_nodes=mesh_pos.shape[0])
        # out_degree = pyg_utils.degree(support_edge[0], num_nodes=mesh_pos.shape[0])
        # node_degree = in_degree + out_degree
        # print("Degree max, mean ,min:", node_degree.max(), node_degree.mean(), node_degree.min())
        
        # # write to file
        # mesh["node_degree"] = node_degree

        # write_hybrid_mesh_to_vtu(
        #     mesh_pos=mesh_pos.cpu().numpy(), 
        #     data_dict={
        #         f"node|in_degree":in_degree.cpu().numpy(),
        #         f"node|out_degree":out_degree.cpu().numpy(),
        #         f"node|node_degree":node_degree.cpu().numpy(),
        #     }, 
        #     cells_node=mesh["cells_node"].cpu(), 
        #     cells_type=mesh["cells_node_ptr"].cpu(),
        #     filename=f"Logger/Grad_test/degree_vis.vtu",
        # )
        ''' 检查模板并绘制度分布 '''


    def _calculate_geometric_properties(self):
        """Calculate face normals, areas, and cell volumes using optimized methods"""
        logging.info("Calculating geometric properties...")

        # Use base class method for face normals and areas
        self.faces_normals, self.faces_areas = self._calculate_face_geometric_properties_vectorized(
            self.faces_raw, self.mesh_pos, self.faces_pos, face_node=self.face_node, face_node_ptr=self.face_node_ptr
        )

        # Calculate cell-face normals and areas
        self._calculate_cell_face_properties()

        # Validate face normals using flux closure test
        self._validate_face_normals()

        # Calculate cell volumes using divergence theorem
        self._calculate_cell_volumes()

        # Validate all volumes are positive
        if torch.any(self.cells_volume <= 0):
            raise ValueError("Found non-positive cell volumes!")

        logging.info("  ✓ All cell volumes are positive")

    def _calculate_cell_face_properties(self):
        """Calculate cell-face normals and areas using vectorized operations"""
        # Get cell centers for all cells that have faces
        cells_centers = self.cells_pos[self.cells_face_ptr]
        faces_centers_expanded = self.faces_pos[self.cells_face]

        # Calculate vectors from cell centers to face centers
        cell_to_face_vectors = faces_centers_expanded - cells_centers

        # Get face normals for each cell-face pair
        faces_normals_expanded = self.faces_normals[self.cells_face]

        # Check orientation and flip if needed
        dot_products = torch.sum(cell_to_face_vectors * faces_normals_expanded, dim=1)
        flip_mask = dot_products < 0

        cells_face_normal = faces_normals_expanded.clone()
        cells_face_normal[flip_mask] = -cells_face_normal[flip_mask]

        # Get face areas for each cell-face pair
        cells_face_area = self.faces_areas[self.cells_face]

        self.cells_face_normal = cells_face_normal
        self.cells_face_area = cells_face_area.reshape(-1, 1)

        logging.info("  Calculated normals and areas for %d faces", len(self.faces_normals))
        logging.info("  Cell-face pairs: %d", len(self.cells_face_normal))

    def _validate_face_normals(self):
        """
        Validate that cells-face normals and areas are correctly computed
        by checking that the sum of (face normal × face area) for each cell is approximately zero.
        This uses the flux closure principle.
        """
        logging.info("  Validating face normals using flux closure test...")

        # Calculate weighted normals (normal * area)
        surface_vector = self.cells_face_normal * self.cells_face_area  # [total_cell_faces, 3]

        # Sum for each cell using scatter
        flux_sum = scatter(
            src=surface_vector,
            index=self.cells_face_ptr,
            dim=0,
            reduce='sum'
        )  # [num_cells, 3]

        # Calculate magnitude of flux sum for each cell
        flux_sum_magnitude = torch.norm(flux_sum, dim=1)  # [num_cells]

        # Calculate total face area for each cell for normalization
        cell_area_sum = scatter(
            src=self.cells_face_area.squeeze(),
            index=self.cells_face_ptr,
            dim=0,
            reduce='sum'
        )  # [num_cells]

        # Normalize by total face area of each cell
        normalized_flux = flux_sum_magnitude / cell_area_sum

        # Print statistics
        logging.info("    Flux closure test results (should be close to zero):")
        logging.info("    Min normalized flux: %.8f", torch.min(normalized_flux).item())
        logging.info("    Max normalized flux: %.8f", torch.max(normalized_flux).item())
        logging.info("    Mean normalized flux: %.8f", torch.mean(normalized_flux).item())
        logging.info("    Median normalized flux: %.8f", torch.median(normalized_flux).item())

        # Count cells with closure error above threshold
        threshold = 1e-5
        bad_cells = torch.sum(normalized_flux > threshold).item()

        if bad_cells > 0:
            logging.warning("    Cells with normalized flux > %s: %d out of %d (%.2f%%)",
                           threshold, bad_cells, len(normalized_flux), bad_cells/len(normalized_flux)*100)
            # Show worst cases
            worst_indices = torch.argsort(normalized_flux, descending=True)[:min(5, bad_cells)]
            logging.warning("    Worst cell flux closure values:")
            for idx in worst_indices:
                logging.warning("      Cell %d: %.8f", idx, normalized_flux[idx].item())
        else:
            logging.info("    ✓ All cells pass flux closure test (normalized flux < %s)", threshold)

        # Raise error if too many cells fail the test
        if bad_cells > len(normalized_flux) * 0.01:  # More than 1% of cells fail
            raise ValueError(f"Flux closure test failed: {bad_cells}/{len(normalized_flux)} cells "
                           f"({bad_cells/len(normalized_flux)*100:.2f}%) have normalized flux > {threshold}")

    def _validate_face_statistics(self):
        """Validate face normal and area statistics"""
        logging.info("  Face normal statistics:")
        face_normal_lengths = torch.norm(self.faces_normals, dim=1)
        logging.info("    Min length: %.6f", torch.min(face_normal_lengths).item())
        logging.info("    Max length: %.6f", torch.max(face_normal_lengths).item())
        logging.info("    Mean length: %.6f", torch.mean(face_normal_lengths).item())
        logging.info("    Std deviation: %.6f", torch.std(face_normal_lengths).item())

        # Check if normals are unit vectors (should be close to 1.0)
        non_unit_normals = torch.sum(torch.abs(face_normal_lengths - 1.0) > 1e-6).item()
        if non_unit_normals > 0:
            logging.warning("    ⚠️  WARNING: %d faces have non-unit normals (may indicate calculation issues)", non_unit_normals)
            # Show some examples
            non_unit_mask = torch.abs(face_normal_lengths - 1.0) > 1e-6
            non_unit_indices = torch.where(non_unit_mask)[0][:3]  # Show first 3
            for idx in non_unit_indices:
                logging.warning("      Face %d: normal length = %.6f", idx, face_normal_lengths[idx].item())
        else:
            logging.info("    ✓ All face normals are unit vectors")

        logging.info("  Face area statistics:")
        logging.info("    Min area: %.6f", torch.min(self.faces_areas).item())
        logging.info("    Max area: %.6f", torch.max(self.faces_areas).item())
        logging.info("    Mean area: %.6f", torch.mean(self.faces_areas).item())
        logging.info("    Total surface area: %.6f", torch.sum(self.faces_areas).item())

        # Check for zero or negative areas
        bad_areas = torch.sum(self.faces_areas <= 0).item()
        if bad_areas > 0:
            logging.error("❌ CRITICAL: Found %d faces with zero or negative areas!", bad_areas)
            raise ValueError(f"Found {bad_areas} faces with zero or negative areas!")
        else:
            logging.info("    ✓ All face areas are positive")

    def _calculate_cell_volumes(self):
        """Calculate cell volumes using divergence theorem"""
        # Get face centers for all cell-face pairs
        faces_centers_expanded = self.faces_pos[self.cells_face]

        # Linear function: f(x) = x at each face center
        linear_function_values = faces_centers_expanded

        # Calculate f·n for each cell-face pair
        flux_values = torch.sum(linear_function_values * self.cells_face_normal, dim=1)

        # Multiply by face areas
        weighted_flux = flux_values * self.cells_face_area.squeeze()

        # Sum for each cell using scatter
        total_flux = scatter(
            src=weighted_flux,
            index=self.cells_face_ptr,
            dim=0,
            reduce='sum'
        )

        # Volume = (1/3) * total_flux (divergence theorem for f(x) = x)
        self.cells_volume = (total_flux / 3.0).reshape(-1, 1)

        # Validate volumes
        negative_volumes = torch.sum(self.cells_volume < 0).item()
        if negative_volumes > 0:
            logging.error("  ❌ CRITICAL: %d cells have negative volumes!", negative_volumes)
            # Show some examples of negative volumes
            negative_mask = self.cells_volume.squeeze() < 0
            negative_indices = torch.where(negative_mask)[0][:5]  # Show first 5
            for idx in negative_indices:
                logging.error("    Cell %d: volume = %.2e", idx, self.cells_volume[idx].item())
            raise ValueError(f"Found {negative_volumes} cells with negative volumes!")
        else:
            logging.info("  ✓ All cell volumes are positive")

    def _calculate_compound_attributes(self):
        """Calculate compound attributes for boundary faces as virtual cells"""
        logging.info("Calculating compound attributes...")

        face_type = self.faces_type.long().squeeze()
        neighbor_cell = self.faces_neighbor_cell.long().T
        face_center_pos = self.faces_pos.to(torch.float32)
        centroid = self.cells_pos.to(torch.float32)

        # Find boundary faces
        BC_face_mask = ~(face_type == NodeType.NORMAL).squeeze()
        BC_face_center_pos = face_center_pos[BC_face_mask]
        BC_face_center_pos_ptr = torch.arange(BC_face_center_pos.shape[0]) + centroid.shape[0]

        # Create compound cell type
        cell_type = torch.cat((
            torch.full((centroid.shape[0],), NodeType.NORMAL, dtype=torch.long),
            face_type[BC_face_mask]
        ), dim=0)

        # Create compound neighbor cell array
        cpd_neighbor_cell = neighbor_cell.clone()
        cpd_neighbor_cell[1, BC_face_mask] = BC_face_center_pos_ptr

        # Create compound centroid
        cpd_centroid = torch.cat((centroid, BC_face_center_pos), dim=0)

        self.cpd_cell_type = cell_type
        self.cpd_neighbor_cell = cpd_neighbor_cell
        self.cpd_centroid = cpd_centroid

        logging.info("  Original cells: %d", centroid.shape[0])
        logging.info("  Boundary faces as cells: %d", BC_face_center_pos.shape[0])
        logging.info("  Total compound cells: %d", cpd_centroid.shape[0])

    def export_to_hdf5(self, output_path: str, case_name: str):
        """Export mesh data to HDF5 format"""
        metadata = {
            'case_path': self.case_path,
            'num_points': self.num_point,
            'num_faces': self.num_face,
            'num_cells': self.num_cell,
            'num_internal_faces': self.num_inner_face
        }

        self.save_to_hdf5(output_path, case_name, **metadata)

        # Additional OpenFOAM-specific datasets
        with h5py.File(output_path, 'a') as hf:
            if self.edge_node is not None:
                hf.create_dataset('edge_node', data=self.edge_node.numpy())
            if self.faces_edge is not None:
                hf.create_dataset('face_edge', data=self.faces_edge.numpy())
            if self.faces_edge_ptr is not None:
                hf.create_dataset('face_edge_ptr', data=self.faces_edge_ptr.numpy())
            if self.cells_face_area is not None:
                hf.create_dataset('cells_face_area', data=self.cells_face_area.numpy())
            if self.cells_face_normal is not None:
                hf.create_dataset('cells_face_normal', data=self.cells_face_normal.numpy())
            if self.cyclic_neighbor_cell is not None:
                hf.create_dataset('cyclic|neighbor_cell', data=self.cyclic_neighbor_cell.numpy())
            if self.edge_node_x is not None:
                hf.create_dataset('edge_node_x', data=self.edge_node_x.numpy())

    def export_vtu_files(self, output_dir: str):
        """Export VTU files for visualization"""
        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)

            # Export full mesh
            self._export_full_mesh_vtu(output_dir)

            # Export boundary faces by type
            self._export_boundary_vtu_files(output_dir)

        except Exception as e:
            logging.error("❌ VTU export failed: %s", e)
            logging.error("   Visualization files will not be available!")

    def _export_full_mesh_vtu(self, output_dir: str):
        """Export complete mesh to VTU format with edge_node_x based degree calculations"""
        case_name = os.path.basename(self.case_path)
        output_path = os.path.join(output_dir, f"{case_name}_full_mesh.vtu")

        # Calculate degrees based on edge_node_x (following the commented code pattern in construct_stencil)
        if hasattr(self, 'edge_node_x') and self.edge_node_x is not None:
            # Import PyG utilities for degree calculation
            from torch_geometric import utils as pyg_utils

            # Calculate in_degree and out_degree from edge_node_x
            # edge_node_x[0] are the source nodes (out-degree contributors)
            # edge_node_x[1] are the target nodes (in-degree contributors)
            edge_node_x_in_degree = pyg_utils.degree(self.edge_node_x[1], num_nodes=self.mesh_pos.shape[0])
            edge_node_x_out_degree = pyg_utils.degree(self.edge_node_x[0], num_nodes=self.mesh_pos.shape[0])
            edge_node_x_node_degree = edge_node_x_in_degree + edge_node_x_out_degree

            logging.info("Edge_node_x degree statistics:")
            logging.info("  In-degree  - max: %.1f, mean: %.1f, min: %.1f",
                        edge_node_x_in_degree.max().item(),
                        edge_node_x_in_degree.mean().item(),
                        edge_node_x_in_degree.min().item())
            logging.info("  Out-degree - max: %.1f, mean: %.1f, min: %.1f",
                        edge_node_x_out_degree.max().item(),
                        edge_node_x_out_degree.mean().item(),
                        edge_node_x_out_degree.min().item())
            logging.info("  Total degree - max: %.1f, mean: %.1f, min: %.1f",
                        edge_node_x_node_degree.max().item(),
                        edge_node_x_node_degree.mean().item(),
                        edge_node_x_node_degree.min().item())

            # Use edge_node_x based degrees
            data_dict = {
                "node|edge_node_x_in_degree": edge_node_x_in_degree.cpu().numpy(),
                "node|edge_node_x_out_degree": edge_node_x_out_degree.cpu().numpy(),
                "node|edge_node_x_node_degree": edge_node_x_node_degree.cpu().numpy(),
            }

            # Also include original degrees for comparison if they exist
            if hasattr(self, 'in_degree') and self.in_degree is not None:
                data_dict["node|original_in_degree"] = self.in_degree.cpu().numpy()
                data_dict["node|original_out_degree"] = self.out_degree.cpu().numpy()
                data_dict["node|original_node_degree"] = self.node_degree.cpu().numpy()
        else:
            # Fallback to original degrees if edge_node_x is not available
            logging.warning("edge_node_x not available, using original degree calculations")
            data_dict = {
                "node|in_degree": self.in_degree.cpu().numpy() if hasattr(self, 'in_degree') else None,
                "node|out_degree": self.out_degree.cpu().numpy() if hasattr(self, 'out_degree') else None,
                "node|node_degree": self.node_degree.cpu().numpy() if hasattr(self, 'node_degree') else None,
            }
            # Remove None values
            data_dict = {k: v for k, v in data_dict.items() if v is not None}

        export_full_mesh_vtu(
            mesh_pos=self.mesh_pos.cpu().numpy(),
            pv_cells_node=self.pv_cells_node.cpu().numpy(),
            pv_cells_type=self.pv_cells_type.cpu().numpy(),
            save_file_path=output_path,
            data_dict=data_dict,
        )

        logging.info("Full mesh exported to: %s", output_path)

    def _export_boundary_vtu_files(self, output_dir: str):
        """Export boundary faces by type to separate VTU files"""
        # Group boundary faces by type
        boundary_faces = {}
        for face_idx in range(len(self.faces_type)):
            face_type = self.faces_type[face_idx].item()
            if face_type not in boundary_faces:
                boundary_faces[face_type] = []
            boundary_faces[face_type].append(face_idx)

        # Export each boundary type
        for face_type, face_indices in boundary_faces.items():
            type_name = self._get_boundary_type_name(face_type)
            self._export_boundary_type_vtu(face_indices, type_name, output_dir)

    def _export_boundary_type_vtu(self, face_indices: List[int], type_name: str, output_dir: str):
        """Export specific boundary type to VTU"""
        try:
            case_name = os.path.basename(self.case_path)
            output_path = os.path.join(output_dir, f"{case_name}_boundary_{type_name}.vtu")

            # Convert to tensor for vectorized operations
            face_indices_tensor = torch.tensor(face_indices, dtype=torch.long)

            if len(face_indices_tensor) == 0:
                logging.info("No faces found for boundary type %s", type_name)
                return

            # Get basic data (following original implementation)
            face_node = self.face_node.long().squeeze()
            faces_edge_ptr = self.faces_edge_ptr.long().squeeze()  # face_node_ptr is same as face_edge_ptr

            # Get node indices for faces of current type (vectorized)
            cur_face_node_mask = torch.isin(faces_edge_ptr, face_indices_tensor)
            cur_face_node = face_node[cur_face_node_mask]
            cur_faces_edge_ptr_raw = faces_edge_ptr[cur_face_node_mask]

            # Vectorized face index remapping (from original implementation)
            max_face_idx = torch.max(face_indices_tensor).item() + 1
            face_global_to_local = torch.full((max_face_idx,), -1, dtype=torch.long)
            face_global_to_local[face_indices_tensor] = torch.arange(len(face_indices_tensor), dtype=torch.long)

            # Apply the mapping to get local face indices
            cur_faces_edge_ptr = face_global_to_local[cur_faces_edge_ptr_raw]

            # Get unique nodes for local mapping
            unique_nodes, node_inverse_indices = torch.unique(cur_face_node, return_inverse=True)

            # Extract positions for unique nodes
            cur_nodes_pos = self.mesh_pos[unique_nodes]
            cur_node_type = self.node_type[unique_nodes]
            
            # Remap face_node indices from global to local
            cur_face_node_local = node_inverse_indices

            # Create face type data for current faces only
            face_type_value = self.faces_type[face_indices_tensor[0]].item()
            faces_type_data = torch.full((len(face_indices_tensor), 1), face_type_value, dtype=torch.long)

            # Extract face normals for current face type
            cur_faces_normals = self.faces_normals[face_indices_tensor]  # [n_faces, 3]

            # Export to VTU with correct face types (not cell types)
            self._write_faces_to_vtu(
                mesh_pos=cur_nodes_pos.cpu().numpy(),
                face_node=cur_face_node_local.cpu(),
                faces_ptr=cur_faces_edge_ptr.cpu(),
                data_dict={
                    "node|node_type":cur_node_type.cpu().numpy(),
                    "face|faces_type": faces_type_data.cpu().numpy(),
                    "face|faces_normal": cur_faces_normals.cpu().numpy(),
                },
                file_path=output_path
            )

            logging.info("Boundary type %s exported: %d faces to %s", type_name, len(face_indices_tensor), output_path)

        except Exception as e:
            logging.error("❌ Failed to export boundary type %s: %s", type_name, e)
            logging.error("   Boundary visualization for %s will not be available!", type_name)

    def _write_faces_to_vtu(self, mesh_pos: np.ndarray, face_node: torch.Tensor, faces_ptr: torch.Tensor,
                           data_dict: Dict, file_path: str):
        """
        Write faces to VTU file with correct 2D face types (triangles, quads, polygons)
        instead of 3D cell types.
        """
        import pyvista as pv

        # Convert to numpy if needed
        if isinstance(face_node, torch.Tensor):
            face_node = face_node.numpy()
        if isinstance(faces_ptr, torch.Tensor):
            faces_ptr = faces_ptr.numpy()

        # Calculate number of nodes per face using scatter
        face_node_counts = torch.bincount(torch.from_numpy(faces_ptr)).numpy()

        # Build face connectivity and types
        pv_cells = []
        pv_cell_types = []

        current_pos = 0
        for face_idx in range(len(face_node_counts)):
            if face_node_counts[face_idx] == 0:
                continue

            num_nodes = face_node_counts[face_idx]
            face_nodes = face_node[current_pos:current_pos + num_nodes]

            # Add connectivity: [num_nodes, node1, node2, ...]
            pv_cells.extend([num_nodes] + face_nodes.tolist())

            # Determine face type based on number of nodes
            if num_nodes == 3:
                pv_cell_types.append(pv.CellType.TRIANGLE)
            elif num_nodes == 4:
                pv_cell_types.append(pv.CellType.QUAD)
            else:
                # For faces with more than 4 nodes, use polygon
                pv_cell_types.append(pv.CellType.POLYGON)

            current_pos += num_nodes

        # Create PyVista UnstructuredGrid
        pv_cells = np.array(pv_cells)
        pv_cell_types = np.array(pv_cell_types)

        grid = pv.UnstructuredGrid(pv_cells, pv_cell_types, mesh_pos)

        # Add data from data_dict
        for key, values in data_dict.items():
            if key.startswith("node|"):
                # Add point data
                grid.point_data[key] = np.array(values)
            elif key.startswith("face|") or key.startswith("cell|"):
                # Add cell data (faces are treated as cells in VTU)
                grid.cell_data[key] = np.array(values)

        # Save the VTU file
        grid.save(file_path, binary=True)
        print(f"VTU file saved at {file_path}")


def convert_openfoam_to_h5_optimized(case_path: str, case_name: str = None, output_path: str = None,
                                    enable_visualization: bool = True,
                                    enable_vertex_order_validation: bool = False,):
    """
    Convert OpenFOAM mesh to HDF5 format using the optimized refactored parser.

    Args:
        case_path: Path to OpenFOAM case directory
        case_name: Name of the case (auto-detected if None)
        output_path: Output path for HDF5 file (auto-generated if None)
        enable_visualization: Whether to create visualization files
        enable_vertex_order_validation: Enable strict face vertex order validation
    """
    # Auto-detect case name if not provided
    if case_name is None:
        case_name = os.path.basename(case_path)

    # Auto-generate output path following original convention: case_path/h5/case_name.h5
    if output_path is None:
        output_dir = os.path.join(case_path, 'h5')
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f"{case_name}.h5")
    try:
        # Initialize optimized manager
        manager = OpenFOAMManager(
            case_path,
            plt_vis=enable_visualization,
            enable_vertex_order_validation=enable_vertex_order_validation,
        )

        # Export to HDF5
        logging.info("Exporting to HDF5...")
        manager.export_to_hdf5(output_path, case_name)

        # Export VTU files following original convention: case_path/h5/vtu_boundary_faces/
        if enable_visualization:
            vtu_output_dir = os.path.join(os.path.dirname(output_path), "vtu_boundary_faces")
            logging.info("Exporting VTU files...")
            manager.export_vtu_files(vtu_output_dir)

        logging.info("OpenFOAM conversion completed successfully!")
        return manager

    except Exception as e:
        logging.error("Error in OpenFOAM conversion: %s", str(e), exc_info=True)
        raise


def main():
    """主函数，支持命令行参数"""
    import sys

    if len(sys.argv) > 1:
        # 从命令行参数获取case路径
        case_path = sys.argv[1]
        case_name = os.path.basename(case_path)
    else:
        # 默认值
        case_path = f'{os.getcwd()}/mesh_example/MMS-40x40x40'
        case_name = os.path.basename(case_path)

    out_path = case_path
    plt_vis = False  # 关闭可视化以提高速度
    enable_vertex_order_validation = False  # 关闭验证以提高速度

    # Configure logging
    logging.basicConfig(
        level=getattr(logging, "INFO"),
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    print(f"Converting OpenFOAM case: {case_path}")

    # Run conversion
    convert_openfoam_to_h5_optimized(
        case_path=case_path,
        case_name=case_name,
        # output_path=out_path, # 不给outpath则默认存在同case目录下
        enable_visualization=plt_vis,
        enable_vertex_order_validation=enable_vertex_order_validation,
    )

    print(f"Conversion completed for {case_name}")

if __name__ == "__main__":
    main()

# ==============================================================================
# HDF5 Data Shape Documentation
# ==============================================================================
#
# This section documents the shape of each dataset saved to the HDF5 file.
# Dataset names use the new HDF5 naming convention implemented in save_to_hdf5().
#
# Naming Conventions:
# - N_points: Total number of unique nodes (vertices) in the mesh.
# - N_faces: Total number of unique faces in the mesh.
# - N_cells: Total number of unique cells (polyhedra) in the mesh.
# - N_edges: Total number of unique edges in the mesh.
# - N_total_cell_nodes: Sum of the number of nodes for each cell.
# - N_total_cell_faces: Sum of the number of faces for each cell.
# - N_total_face_nodes: Sum of the number of nodes for each face.
# - N_total_face_edges: Sum of the number of edges for each face.
#
# ------------------------------------------------------------------------------
# HDF5 Dataset Name      | Shape                     | Description
# ------------------------------------------------------------------------------
#
# === Geometry Data ===
# node|pos               | (N_points, 3)             | XYZ coordinates for each node.
# face|face_center_pos   | (N_faces, 3)              | XYZ coordinates for the center of each face.
# cell|centroid          | (N_cells, 3)              | XYZ coordinates for the center of each cell.
#
# === Connectivity Data (Pointer-based) ===
# cells_node             | (N_total_cell_nodes, 1)   | Node indices belonging to cells.
# cells_node_ptr         | (N_total_cell_nodes, 1)   | Cell index for each entry in cells_node.
#
# cells_face             | (N_total_cell_faces, 1)   | Face indices belonging to cells.
# cells_face_ptr         | (N_total_cell_faces, 1)   | Cell index for each entry in cells_face.
#
# face_node              | (N_total_face_nodes, 1)   | Node indices belonging to faces.
# face_node_ptr          | (N_total_face_nodes, 1)   | Face index for each entry in face_node.
#
# face_edge              | (N_total_face_edges, 1)   | Edge indices belonging to faces.
# face_edge_ptr          | (N_total_face_edges, 1)   | Face index for each entry in face_edge.
#
# === Connectivity Data (Direct Mapping) ===
# edge_node              | (N_edges, 2)              | Node indices for each unique-dir edge.
# edge_node_x            | (N_edges+extra_edges, 2)  | Node indices for each unique-dir edge for WLSQ stencil.
# face|neighbor_cell     | (N_faces, 2)              | Cell indices for the two cells neighboring each face.
#                                                     | For boundary faces, both indices are the same.
#
# === Attribute Data ===
# node|node_type         | (N_nodes, 1)              | Type of node FOLLOW the priority: WALL>INFLOW=OUTFLOW>CYCLIC.
# face|face_type         | (N_faces, 1)              | Integer code for the type of each face (e.g., boundary, internal).
# face|face_area         | (N_faces, 1)              | Area of each face.
# cells_volume          | (N_cells, 1)              | Volume of each cell.
#
# === Cell-Face Pair Data ===
# cells_face_area       | (N_total_cell_faces, 1)   | Area of each face, repeated for each cell it belongs to.
# cells_face_normal     | (N_total_cell_faces, 3)   | Normal vector of each face, oriented outwards from its cell.
#
# === Compound Attributes (cpd|*) ===
# These attributes combine original cells with boundary faces treated as virtual cells
# N_compound_cells = N_cells + N_boundary_faces
#
# cpd|cell_type          | (N_compound_cells, 1)     | Type of each compound cell: NORMAL for original cells,
#                                                     | boundary condition type for boundary face centers.
# cpd|neighbor_cell      | (2, N_faces)              | Modified neighbor cell indices where boundary faces
#                                                     | point to their corresponding virtual boundary cells.
# cpd|centroid           | (N_compound_cells, 3)     | Coordinates combining original cell centers and
#                                                     | boundary face centers as virtual cells.
#
# ==============================================================================