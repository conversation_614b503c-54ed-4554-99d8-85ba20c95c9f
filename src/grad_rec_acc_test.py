import sys
import os

cur_path = os.path.split(__file__)[0]
sys.path.append(cur_path)

import torch
import numpy as np

# import os
from FVdomain import Graph_loader
from Utils import get_param
import time
from Utils.get_param import get_hyperparam
from Utils.Logger import Logger
from Post_process.to_vtk import export_full_mesh_vtu
import random
import datetime
from FVmodel.FVdiscretization.FVgrad import gradient_reconstruction,compute_normal_matrix,green_gauss_gradient
from Utils.utilities import NodeType

# configurate parameters
params = get_param.params()
seed = int(datetime.datetime.now().timestamp())
np.random.seed(seed)
random.seed(seed)
torch.manual_seed(seed)
torch.cuda.set_per_process_memory_fraction(0.8, params.on_gpu)

device = "cuda"
params.dataset_dir="/home/<USER>/mycode/Gen-FVGN-3D/mesh_example/lidDrivenCavity3D-simple"
params.dataset_size=1
params.batch_size=1
params.order = "2nd" # 1st, 2nd, 3rd, 4th, GG

# initialize Logger and load model / optimizer if according parameters were given
logger = Logger(
    get_hyperparam(params),
    use_csv=True,
    use_tensorboard=False,
    params=params,
    copy_code=True,
    seed=seed,
)

# initialize Training Dataset
start = time.time()
datasets_factory = Graph_loader.DatasetFactory(
    params=params,
    dataset_dir=params.dataset_dir,
    state_save_dir=logger.saving_path,
    device=device,
)

# refresh dataset size
params.dataset_size = datasets_factory.dataset_size

# create dataset objetc
datasets, loader = datasets_factory.create_loader(
    batch_size=params.batch_size, num_workers=0, pin_memory=False, shuffle=False
)

end = time.time()
print("Training traj has been loaded time consuming:{0}".format(end - start))

''' >>> fetch data and move to GPU >>> '''
fv_graph = next(iter(loader))
fv_graph = fv_graph.to(device, exclude_keys=['global_idx'])
''' <<< fetch data and move to GPU <<< '''

# calcualate phi node value for 3D
from Utils.utilities import Scalar_Eular_solution_3D

# Use 3D version for 3D meshes
phi_cell_GT, nabla_phi_GT, hessian_phi_GT = Scalar_Eular_solution_3D(
    mesh_pos=fv_graph.graph_cell.cpd_centroid,
    phi_0=5.0,
    phi_x=0.01,
    phi_y=0.01,
    phi_z=0.01,  # z component for 3D
    phi_xy=0.01,
    phi_xz=0.01,  # xz component for 3D
    phi_yz=0.01,  # yz component for 3D
    alpha_x=5,
    alpha_y=5,
    alpha_z=5,  # z component for 3D
    alpha_xy=5,
    alpha_xz=5,  # xz component for 3D
    alpha_yz=5,  # yz component for 3D
    L=1.0,
    device=device,
)

internal_cell_mask = (fv_graph.graph_cell.cpd_cell_type== NodeType.NORMAL).squeeze()

''' >>> Weighted-LSQ >>> '''
(A_cell_to_cell, two_way_B_cell_to_cell) = compute_normal_matrix(
    order=params.order,
    mesh_pos=fv_graph.graph_cell.cpd_centroid,
    edge_index=fv_graph.graph_cell_x.neighbor_cell_x, # 默认应该是仅包含1阶邻居点+构成共点的单元的所有点
)
singleway_B = torch.chunk(two_way_B_cell_to_cell, 2, dim=0)[0]

grad_phi = gradient_reconstruction(
    order="2nd", 
    phi_node=phi_cell_GT, 
    edge_index=fv_graph.graph_cell_x.neighbor_cell_x, 
    mesh_pos=fv_graph.graph_cell.cpd_centroid, 
    precompute_Moments=[A_cell_to_cell,singleway_B]
)[internal_cell_mask]# 只取内部cell的梯度
''' <<< Weighted-LSQ <<< '''




'''>>> calculate the relative L2 error for gradients >>>'''
Grad_Relative_l2 = torch.norm(
    grad_phi[:, 0, 0:3] - nabla_phi_GT[internal_cell_mask, 0:3], dim=0
) / torch.norm(nabla_phi_GT[internal_cell_mask, 0:3], dim=0)

Grad_MSE = torch.mean((grad_phi[:, 0, 0:3] - nabla_phi_GT[internal_cell_mask, 0:3])**2)

print(f"Gradient Relative L2 error: {Grad_Relative_l2}")
print(f"Gradient MSE: {Grad_MSE}")
'''<<< calculate the relative L2 error for gradients <<<'''

''' 将梯度保存到vtu文件 '''
cpd_cell_type = fv_graph.graph_cell.cpd_cell_type
interior_cell_mask = (cpd_cell_type== NodeType.NORMAL)
father_dir = os.path.dirname(logger.saving_path)
for _ in range(1):
    father_dir = os.path.dirname(father_dir)
case_name = "".join(
    chr(code) for code in fv_graph.graph_cell.case_name.cpu().tolist()
)
os.makedirs(f"{father_dir}/Grad_test", exist_ok=True)
# Prepare VTU output data for 3D meshes
vtu_data = {
    "cell|phi_cell_GT": phi_cell_GT[interior_cell_mask].cpu().numpy(),
    "cell|grad_phi_GT": nabla_phi_GT[interior_cell_mask].cpu().numpy(),
    "cell|grad_phi": grad_phi[:,0,0:3].cpu().numpy(),  # 3D gradients
}
export_full_mesh_vtu(
    mesh_pos=datasets.meta_pool[fv_graph.graph_cell.graph_index.cpu().item()]["node|pos"], 
    pv_cells_node=datasets.meta_pool[fv_graph.graph_cell.graph_index.cpu().item()]["pv_cells_node"], 
    pv_cells_type=datasets.meta_pool[fv_graph.graph_cell.graph_index.cpu().item()]["pv_cells_type"],
    save_file_path=f"{father_dir}/Grad_test/grad_{case_name}_{params.order}.vtu",
    data_dict=vtu_data
)
''' 将梯度保存到vtu文件 '''
