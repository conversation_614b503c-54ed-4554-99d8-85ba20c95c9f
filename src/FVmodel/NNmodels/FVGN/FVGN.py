import sys
import os
file_dir = os.path.dirname(__file__)
sys.path.append(file_dir)
import torch
import torch.nn as nn
from FVmodel.NNmodels.FVGN.EPD import Encoder, Decoder, GnBlock
from FVmodel.NNmodels.GraphTransolver.GraphTransolver import Transolver_block
from torch_geometric.data import Data
from torch_scatter import scatter_add, scatter_mean
from FVmodel.NNmodels.FVGN.EPD import build_mlp_from_num_layer
from Utils.utilities import NodeType


class Processor(nn.Module):
    def __init__(self,message_passing_num=0, hidden_size=128, drop_out=False):
        super(Processor, self).__init__()

        if message_passing_num<1:
            raise ValueError("message_passing_num must be greater than 0")
        
        GN_block_list = []
        for _ in range(message_passing_num):
            GN_block_list.append(
                GnBlock(
                    hidden_size=hidden_size,
                    drop_out=drop_out,
                )
            )
        self.GN_block_list = nn.ModuleList(GN_block_list)


    def forward(self, latent_graph):

        # 处理剩余的模型
        for model in self.GN_block_list:
            latent_graph = model(latent_graph)

        return latent_graph


class Simulator(nn.Module):
    def __init__(
        self,
        message_passing_num,
        edge_input_size,
        node_input_size,
        node_output_size,
        drop_out=False,
        hidden_size=128,
        params=None,
    ):
        super(Simulator, self).__init__()

        self.encoder = Encoder(
            node_input_size=node_input_size,
            edge_input_size=edge_input_size,
            hidden_size=hidden_size,
        )

        self.processer = Processor(
                    message_passing_num=message_passing_num, 
                    hidden_size=hidden_size, 
                    drop_out=False
                )

        self.decoder = Decoder(
            hidden_size=hidden_size,
            node_output_size=node_output_size,
        )
        
        self.p_decoder = build_mlp_from_num_layer(
            hidden_size,
            hidden_size,
            out_size=1,
            drop_out=False,
            lay_norm=False,
            num_layer=2,
        )
        
    # @torch.compile
    def forward(
        self,
        graph_cell=None,
        cells_node=None,
        cells_node_ptr=None,
        num_points=None,
    ):

        latent_graph_cell, cell_embedding = self.encoder(graph_cell)

        latent_graph_cell = self.processer(latent_graph_cell)

        mask_interior_cell = graph_cell.cell_type == NodeType.NORMAL
        latent_node = scatter_add(
            src=latent_graph_cell.x[mask_interior_cell][cells_node_ptr], 
            index=cells_node, 
            dim=0, 
            dim_size=num_points
        )
        
        pred_cell = self.decoder(latent_graph_cell)
        pred_node = self.p_decoder(latent_node)

        return pred_cell.to(torch.float32), pred_node.to(torch.float32)