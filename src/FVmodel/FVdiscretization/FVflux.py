import sys
import os
file_dir = os.path.dirname(__file__)
sys.path.append(file_dir)

import torch
import torch.nn as nn
from Utils.utilities import (
    decompose_and_trans_node_attr_to_cell_attr_graph,
    copy_geometric_data,
    NodeType,
    calc_cell_centered_with_node_attr,
    calc_node_centered_with_cell_attr,
)
from torch_geometric.data import Data
import numpy as np
from torch_scatter import scatter_add, scatter_mean
from FVmodel.FVdiscretization.FVInterpolation import FV_Interpolation
from torch_geometric.nn import global_add_pool,global_mean_pool

class FV_flux(FV_Interpolation):
    def __init__(self, conserved=True):
        """
        Finite Volume flux calculation class for computing convective, diffusive, and pressure fluxes.
        
        Inherits from Interplot to provide interpolation functionality for flux calculations.
        Used as a base class for flux-related operations in finite volume discretization.
        
        Args:
            conserved (bool): Flag to indicate if conserved form of equations is used.
                            Default is True.
        """
        super(FV_flux, self).__init__()
        
        self.conserved = conserved

    def convective_flux(self, uvw_face_hat):
        """
        Compute convective flux tensor for momentum equations.
        
        Calculates the convective flux using the tensor product of velocity vectors
        at face centers, weighted by convection coefficients.
        
        Args:
            uvw_face_hat (torch.Tensor): Velocity values at face centers [N_faces, 3].
                                       Contains u, v, w velocity components.
        
        Returns:
            torch.Tensor: Convective flux tensor [N_cells_faces, 3, 3].
                         Each element [i,j,k] represents the flux of momentum component j
                         in direction k through face i.
        """
        # Compute velocity tensor product: u_i * u_j
        uu_flux = torch.matmul(
            uvw_face_hat[:,:,None], uvw_face_hat[:,None,:]
        )
        
        # gated by convection coefficient and map to cell faces
        convection_flux = uu_flux[self.cells_face] * \
            self.convection_coefficent[self.cells_face_ptr].unsqueeze(1)
            
        return convection_flux
        
    def diffusion_flux(self, grad_uvw_face_hat):
        """
        Compute viscous (diffusion) flux for momentum equations.
        
        Calculates the diffusive flux using velocity gradients at face centers,
        weighted by diffusion (viscosity) coefficients.
        
        Args:
            grad_uvw_face_hat (torch.Tensor): Velocity gradients at face centers 
                                            [N_faces, 3, 3]. Shape is [face, velocity_component, spatial_derivative].
        
        Returns:
            torch.Tensor: Viscous flux tensor [N_cells_faces, 3, 3].
                         Represents the diffusive momentum flux through each cell face.
        """
        vis_flux = grad_uvw_face_hat[self.cells_face] * \
                self.diffusion_coefficent[self.cells_face_ptr, None]
                
        return vis_flux
    
    def pressure_flux(self, p_face_new):
        """
        Compute pressure flux for momentum equations.
        
        Calculates the pressure flux using pressure values at face centers,
        weighted by pressure gradient coefficients. The pressure acts normal
        to each face surface.
        
        Args:
            p_face_new (torch.Tensor): Pressure values at face centers [N_faces, 1].
        
        Returns:
            torch.Tensor: Pressure flux tensor [N_cells_faces, 3, 3].
                         Diagonal tensor with pressure contribution to momentum flux.
        """
        P_flux = torch.diag_embed(p_face_new[self.cells_face].expand(-1, 3)) * \
            self.grad_p_coefficent[self.cells_face_ptr, None]
        
        return P_flux
    
    def continuity_flux(self, uvw_face_new):
        """
        Compute continuity equation residual using conserved form.
        
        Calculates the mass flux through cell faces to evaluate the continuity equation.
        Uses the divergence theorem to compute the net mass flux for each cell.
        
        Args:
            uvw_face_new (torch.Tensor): Velocity values at face centers [N_faces, 3].
                                       Contains u, v, w velocity components.
        
        Returns:
            torch.Tensor: Continuity equation residual [N_graphs, 1].
                         RMS of mass conservation violations for each graph in the batch.
        """
        # Compute mass flux through each face: velocity · surface_vector
        mass_flux = torch.matmul(
            uvw_face_new[self.cells_face, None, 0:3], 
            self.cells_face_surface_vec[:, :, None]
        ).squeeze()
        
        # Sum fluxes for each cell using scatter operation
        cell_divergence = scatter_add(
            mass_flux,
            self.cells_face_ptr,
            dim=0,
            dim_size=self.cells_volume.shape[0],
        ).view(-1, 1)
        
        # Compute RMS continuity residual for each graph in batch
        loss_cont = torch.sqrt(
            global_add_pool(
                (cell_divergence)**2, batch=self.batch_cell
            )
        ) * self.continuity_eq_coefficent
        
        return loss_cont

            