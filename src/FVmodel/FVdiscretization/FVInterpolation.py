import sys
import os
file_dir = os.path.dirname(__file__)
sys.path.append(file_dir)

import torch
import torch.nn as nn
import torch.jit as jit
from typing import Optional, Tuple
from Utils.utilities import (
    decompose_and_trans_node_attr_to_cell_attr_graph,
    copy_geometric_data,
    NodeType,
    calc_cell_centered_with_node_attr,
    calc_node_centered_with_cell_attr,
)
from torch_geometric.data import Data
import numpy as np
from torch_scatter import scatter_add, scatter_mean
from FVmodel.FVdiscretization.FVbase import FV_base

class FV_Interpolation(FV_base):
    """
    Comprehensive interpolation module for finite volume methods.
    
    This class provides various interpolation schemes for transferring values between
    different locations in a computational mesh (nodes, faces, and cell centers).
    It supports high-order interpolation schemes and RBF-based interpolation.
    
    The interpolation methods are based on finite volume discretization techniques
    described in <PERSON><PERSON><PERSON> et al., "The Finite Volume Method in Computational Fluid 
    Dynamics" (2016).
    
    Implements caching mechanisms to avoid repeated computation of geometric coefficients
    such as interpolation weights (gC, gF) and geometric vectors (CF, eCF).
    """
    
    def __init__(self):
        """
        Initialize the interpolation module.
        
        Sets up caching structures for geometric coefficients to optimize
        repeated interpolation operations.
        """
        super().__init__()
        
        # Cached interpolation coefficients (computed once in calc_geometry_coeff)
        self.gC = None              # Interpolation weight for C (sender) cells
        self.gF = None              # Interpolation weight for F (receiver) cells  
        self.CF = None              # Vector from C to F cell centers
        self.dCF = None             # Distance between C and F cell centers
        self.eCF = None             # Unit vector from C to F
        self.eCf = None             # Vector from C center to face center
        self.eFf = None             # Vector from F center to face center
        
        # Cached cell indices (computed once to avoid repeated index operations)
        self.C_senders = None       # Sender cell indices for interior faces
        self.F_receivers = None     # Receiver cell indices for interior faces

    def register_geometrics(self, graph_cell, graph_face, graph_Index):
        """
        Register and cache geometric quantities and PDE coefficients.
        
        Extracts geometry information from graph structures and caches frequently
        used quantities to avoid repeated computation during interpolation.
        
        Args:
            graph_cell: Cell-based graph containing cell geometry and properties
            graph_face: Face-based graph containing face geometry and connectivity  
            graph_Index: Index graph containing PDE coefficients and batch information
        """
        self.num_graphs = graph_cell.num_graphs

        # Geometry quantities - extract and reshape for efficient computation
        self.cells_face = graph_face.cells_face.view(-1)
        self.cells_face_ptr = graph_cell.cells_face_ptr.view(-1)
        self.cells_volume = graph_cell.cells_volume.view(-1, 1)
        self.cells_face_unv = graph_cell.cells_face_unv.view(-1, 3)
        self.face_type = graph_face.face_type.view(-1)
        self.face_center_pos = graph_face.pos.view(-1, 3)
        self.face_area = graph_face.face_area.view(-1, 1)
        
        self.cpd_cell_type = graph_cell.cpd_cell_type.view(-1)
        self.cpd_centroid = graph_cell.cpd_centroid.view(-1, 3)
        self.cpd_neighbor_cell = graph_cell.cpd_neighbor_cell
        
        # Compute logical masks for different cell/face types
        self.mask_interior_face = (self.face_type == NodeType.NORMAL)
        self.mask_boundary_face = ~self.mask_interior_face
        self.mask_interior_cell = (self.cpd_cell_type == NodeType.NORMAL)
        self.mask_boundary_cell = ~self.mask_interior_cell
        self.mask_dirichlet = (
            (graph_cell.cpd_cell_type == NodeType.WALL_BOUNDARY) | 
            (graph_cell.cpd_cell_type == NodeType.INFLOW)
        ).squeeze()

        # Compute surface vectors for flux calculations
        self.cells_face_surface_vec = self.cells_face_unv * (self.face_area[self.cells_face])
        
        # Batch information for scatter operations
        self.batch_face = graph_face.batch
        self.batch_cell = graph_cell.batch[self.mask_interior_cell]
        
        # Extract PDE coefficients for interior cells only
        self.theta_PDE_cell = graph_Index.theta_PDE[self.batch_cell]
        self.continuity_eq_coefficent = graph_Index.theta_PDE[:, 1:2]
        self.unsteady_coefficent = self.theta_PDE_cell[:, 0:1]
        self.convection_coefficent = self.theta_PDE_cell[:, 2:3]
        self.grad_p_coefficent = self.theta_PDE_cell[:, 3:4]
        self.diffusion_coefficent = self.theta_PDE_cell[:, 4:5]
        self.source_term = self.theta_PDE_cell[:, 5:6] * self.cells_volume
        
        self.dt_cell = graph_Index.dt_graph[self.batch_cell, :]
        
        # Compute and cache interpolation geometric coefficients
        self.calc_geometry_coeff()

    def calc_geometry_coeff(self):
        """
        Compute and cache geometric coefficients for face interpolation.
        
        Calculates interpolation weights (gC, gF) and geometric vectors (CF, eCF)
        based on the method described in Moukalled et al. (2016), Section 9.2.
        These coefficients are cached to avoid repeated computation during 
        multiple interpolation operations.
        
        The coefficients are computed only for interior faces to avoid 
        unnecessary calculations for boundary faces.
        """
        if self.gC is None:
            # Extract sender (C) and receiver (F) cell indices for interior faces
            # Following Moukalled notation: C = upstream cell, F = downstream cell
            self.C_senders = self.cpd_neighbor_cell[0, self.mask_interior_face]
            self.F_receivers = self.cpd_neighbor_cell[1, self.mask_interior_face]
            
            # Compute geometric vectors and distances
            # CF: vector from cell C center to cell F center
            self.CF = (self.cpd_centroid[self.F_receivers] - 
                      self.cpd_centroid[self.C_senders])[:, :, None]
            
            # dCF: distance between cell centers
            self.dCF = torch.norm(self.CF, dim=1, keepdim=True)
            
            # Compute interpolation weights using distance-based weighting
            # gC: weight for upstream cell C, gF: weight for downstream cell F
            face_to_F_distance = torch.norm(
                self.cpd_centroid[self.F_receivers] - self.face_center_pos[self.mask_interior_face],
                dim=1, keepdim=True
            )[:, :, None]
            self.gC = (face_to_F_distance / self.dCF)
            self.gF = 1.0 - self.gC
            
            # Unit vector from C to F
            self.eCF = self.CF / self.dCF
            
            # Vectors from cell centers to face center (for gradient correction)
            self.eCf = (self.face_center_pos[self.mask_interior_face] - 
                       self.cpd_centroid[self.C_senders])[:, :, None]
            self.eFf = (self.face_center_pos[self.mask_interior_face] - 
                       self.cpd_centroid[self.F_receivers])[:, :, None]
        else:
            # Coefficients already computed, skip recalculation
            pass
        
    def interpolating_gradients_to_faces(
        self,
        phi_cell: torch.Tensor,
        grad_phi_cell: torch.Tensor,
    ) -> torch.Tensor:
        """
        Interpolate gradients from cell centers to face centers using high-order interpolation.
        
        Implements the gradient interpolation scheme from Moukalled et al.,
        "The Finite Volume Method in Computational Fluid Dynamics", Section 9.4, page 289.
        
        Uses pre-computed geometric coefficients to avoid repeated calculations.
        Applies gradient correction to maintain high-order accuracy.
        
        Args:
            phi_cell (torch.Tensor): Cell-centered scalar values [N_cells, N_variables]
            grad_phi_cell (torch.Tensor): Cell-centered gradients [N_cells, N_variables, 3]
                                        3 spatial dimensions for gradient components
            
        Returns:
            torch.Tensor: Face-centered gradients [N_interior_faces, N_variables, 3]
                         High-order accurate gradients at interior face centers
            
        Note:
            Requires register_geometrics() to be called first to compute geometric coefficients.
            
        References:
            Moukalled, F., Mangani, L., & Darwish, M. (2016). The finite volume method 
            in computational fluid dynamics. Springer, Section 9.4.
        """
        # Expand phi_cell for broadcasting with geometric coefficients
        phi_cell_expanded = phi_cell[:, :, None]
        
        # Compute interpolated gradient using distance-weighted averaging
        grad_f_hat = (grad_phi_cell[self.C_senders] * self.gC + 
                     grad_phi_cell[self.F_receivers] * self.gF)
        
        # Apply correction term to maintain high-order accuracy
        # Correction ensures consistency between interpolated gradient and cell values
        gradient_dot_eCF = grad_f_hat @ self.eCF
        phi_difference_normalized = ((phi_cell_expanded[self.F_receivers] - 
                                    phi_cell_expanded[self.C_senders]) / self.dCF)
        
        correction = ((phi_difference_normalized - gradient_dot_eCF) * 
                     (self.eCF.transpose(1, 2)))

        return (grad_f_hat + correction).squeeze()

    def interpolating_phic_to_faces(
        self,
        phi_cell: torch.Tensor,
        grad_phi_cell: torch.Tensor,
    ) -> torch.Tensor:
        """
        Interpolate scalar values from cell centers to face centers using high-order interpolation.
        
        Implements the scalar interpolation scheme from Moukalled et al.,
        "The Finite Volume Method in Computational Fluid Dynamics", Section 9.2, page 276.
        
        Uses distance-weighted linear interpolation with gradient-based correction
        to achieve high-order accuracy. Pre-computed geometric coefficients are used
        to optimize performance.
        
        Args:
            phi_cell (torch.Tensor): Cell-centered scalar values [N_cells, N_variables]
            grad_phi_cell (torch.Tensor): Cell-centered gradients [N_cells, N_variables, 3]
                                        Used for high-order correction term
            
        Returns:
            torch.Tensor: Face-centered scalar values [N_interior_faces, N_variables]
                         High-order accurate values at interior face centers
            
        Note:
            Requires register_geometrics() to be called first to compute geometric coefficients.
            
        References:
            Moukalled, F., Mangani, L., & Darwish, M. (2016). The finite volume method 
            in computational fluid dynamics. Springer, Section 9.2.
        """
        # Expand phi_cell for broadcasting with geometric coefficients  
        phi_cell_expanded = phi_cell[:, :, None]
        
        # Compute interpolated value using distance-weighted averaging
        phi_f_hat = (phi_cell_expanded[self.C_senders] * self.gC + 
                    phi_cell_expanded[self.F_receivers] * self.gF)
        
        # Apply gradient-based correction for high-order accuracy
        # Correction accounts for non-uniform mesh spacing and improves accuracy
        correction = (self.gC * grad_phi_cell[self.C_senders] @ self.eCf +
                     self.gF * grad_phi_cell[self.F_receivers] @ self.eFf)
        
        return (phi_f_hat + correction).squeeze()

    def rbf_interpolate(
        self,
        phi_values: torch.Tensor,
        source_pos: torch.Tensor,
        target_pos: torch.Tensor,
        source_indices: torch.Tensor,
        target_indices: torch.Tensor,
        k: int = 4,
        shape_param: float = 0.23
    ) -> torch.Tensor:
        """
        High-performance Radial Basis Function (RBF) interpolation for arbitrary source-to-target mapping.
        
        Implements an optimized RBF interpolation scheme using vectorized operations
        for maximum computational efficiency. Suitable for complex interpolation
        scenarios not covered by standard finite volume interpolation methods.
        
        Args:
            phi_values (torch.Tensor): Source point values [N_source, N_variables]
            source_pos (torch.Tensor): Source point positions [N_source, N_spatial_dims]
            target_pos (torch.Tensor): Target point positions [N_target, N_spatial_dims]
            source_indices (torch.Tensor): Source point indices [N_connections]
            target_indices (torch.Tensor): Target point indices [N_connections]
            k (int, optional): Number of neighbors for each target point. Default: 4
            shape_param (float, optional): RBF shape parameter. Default: 0.23
            
        Returns:
            torch.Tensor: Interpolated values at target points [N_target, N_variables]
            
        Examples:
            Node-to-cell interpolation:
            >>> cell_values = self.rbf_interpolate(
            ...     phi_values=node_phi, source_pos=mesh_pos, target_pos=centroid,
            ...     source_indices=cells_node, target_indices=cells_index
            ... )
            
            Cell-to-node interpolation:
            >>> node_values = self.rbf_interpolate(
            ...     phi_values=cell_phi, source_pos=centroid, target_pos=mesh_pos,
            ...     source_indices=cells_index, target_indices=cells_node
            ... )
            
        Note:
            Uses multiquadric RBF kernel: φ(r) = √(r² + c²) where c is shape_param.
            Optimized for batch processing with vectorized distance calculations.
        """
        n_target = target_pos.size(0)
        n_features = phi_values.size(1)
        
        # Reorganize data for efficient batch processing
        source_pos_neighbors = source_pos[source_indices].view(n_target, k, -1)
        source_phi_neighbors = phi_values[source_indices].view(n_target, k, n_features)
        
        # Compute inter-source distance matrix using optimized vectorization
        neighbors_diff = (source_pos_neighbors.unsqueeze(2) - 
                         source_pos_neighbors.unsqueeze(1))
        distances_squared = torch.sum(neighbors_diff * neighbors_diff, dim=-1)
        
        # Compute RBF kernel matrix using multiquadric basis function
        shape_param_sq = shape_param * shape_param
        kernel = torch.sqrt(distances_squared + shape_param_sq)
        
        # Solve RBF system using batch linear solver
        coeffs = torch.linalg.solve(kernel, source_phi_neighbors)
        
        # Compute target-to-source distances in single operation
        target_pos_expanded = target_pos[target_indices].view(n_target, k, -1)
        target_diff = target_pos_expanded - source_pos_neighbors
        target_distances_squared = torch.sum(target_diff * target_diff, dim=-1)
        
        # Evaluate RBF at target points
        kernel_target = torch.sqrt(target_distances_squared + shape_param_sq).unsqueeze(-1)
        
        # Compute final interpolated values using optimized matrix operations
        result = torch.sum(kernel_target * coeffs, dim=1)
        
        return result
