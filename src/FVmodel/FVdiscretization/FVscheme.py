import sys
import os
file_dir = os.path.dirname(__file__)
sys.path.append(file_dir)

import torch
import torch.nn as nn
from Utils.utilities import (
    decompose_and_trans_node_attr_to_cell_attr_graph,
    copy_geometric_data,
    NodeType,
    calc_cell_centered_with_node_attr,
    calc_node_centered_with_cell_attr,
)
from torch_geometric.data import Data
import numpy as np
from torch_scatter import scatter_add, scatter_mean
from FVmodel.FVdiscretization.FVflux import FV_flux
from FVmodel.FVdiscretization.FVgrad import gradient_reconstruction
from torch_geometric.nn import global_add_pool, global_mean_pool

class Intergrator(FV_flux):
    def __init__(self, params):
        """
        Finite Volume integrator class for assembling and solving discretized equations.
        
        Inherits from FV_flux to provide flux calculation capabilities.
        Handles both conserved and non-conserved forms of the Navier-Stokes equations.
        
        Args:
            params: Configuration object containing discretization parameters,
                   time integration scheme, and equation form settings.
        """
        super(Intergrator, self).__init__()
        self.params = params

    def enforce_boundary_condition(self, uvwp, graph_cell):
        """
        Enforce Dirichlet boundary conditions on the solution vector.
        
        Sets the velocity components at Dirichlet boundary cells to the specified
        boundary values from the graph data.
        
        Args:
            uvwp (torch.Tensor): Solution vector containing [u, v, w, p] values [N_cells, 4].
            graph_cell: Cell graph containing boundary condition values.
            
        Returns:
            torch.Tensor: Updated solution vector with boundary conditions applied.
        """
        uvwp[self.mask_dirichlet, 0:3] = graph_cell.y[:, 0:3]
        return uvwp
    
    def time_scheme(self, uvw_cell_old, uvwp_cell_new):
        """
        Apply temporal discretization scheme for time integration.
        
        Computes the convective velocity field and unsteady term based on the
        selected time integration scheme (explicit, implicit, or IMEX).
        
        Args:
            uvw_cell_old (torch.Tensor): Previous time step velocity [N_cells, 3].
            uvwp_cell_new (torch.Tensor): Current time step solution [N_cells, 4].
            
        Returns:
            tuple: (unsteady_cell, uvw_hat_cell)
                - unsteady_cell (torch.Tensor): Unsteady term contribution [N_interior_cells, 3].
                - uvw_hat_cell (torch.Tensor): Convective velocity field [N_cells, 3].
        """
        # Select convective velocity based on time integration scheme
        if self.params.integrator == "explicit":
            # Use previous time step velocity for convection
            uvw_hat_cell = uvw_cell_old[:, 0:3]
        elif self.params.integrator == "implicit":
            # Use current time step velocity for convection
            uvw_hat_cell = uvwp_cell_new[:, 0:3]
        elif self.params.integrator == "imex":
            # Use average of previous and current for IMEX scheme
            uvw_hat_cell = (
                uvw_cell_old[:, 0:3] + uvwp_cell_new[:, 0:3]
            ) / 2.0
        else:
            raise ValueError(f"Unknown integrator type: {self.params.integrator}")

        # Compute unsteady term: ∂u/∂t * V * coefficient  
        # Note: Apply masks to ensure dimensional consistency
        unsteady_cell = (
            ((uvwp_cell_new[self.mask_interior_cell, 0:3] - uvw_cell_old[self.mask_interior_cell, 0:3]) / self.dt_cell)
        ) * self.cells_volume * self.unsteady_coefficent
        
        return unsteady_cell, uvw_hat_cell
    
    def conserved_form(
        self,
        unsteady_cell,
        uvwp_new=None,
        uvw_hat=None,
        uvw_old=None,
        uvwp_collection=None,
        grad_phi=None,
    ):
        """
        Compute the conserved form of the finite volume equations.
        
        Applies the finite volume method using flux-based formulation with
        conservation of mass and momentum through cell faces.
        
        Args:
            unsteady_cell (torch.Tensor): Unsteady term contribution [N_interior_cells, 3].
            uvwp_new (torch.Tensor): Current solution vector [N_cells, 4].
            uvw_hat (torch.Tensor): Convective velocity field [N_cells, 3].
            uvw_old (torch.Tensor): Previous time step velocity [N_cells, 3].
            uvwp_collection (torch.Tensor): Combined solution vector [N_cells, 7].
            grad_phi (torch.Tensor): Reconstructed gradients [N_cells, 7, 3].
            
        Returns:
            tuple: (loss_cont, loss_momentum, loss_press, uvwp_cell_new)
                - loss_cont: Continuity equation residual
                - loss_momentum: Momentum equation residual  
                - loss_press: Pressure outlet boundary condition residual
                - uvwp_cell_new: Updated solution vector
        """
        """>>> Interpolation >>>"""
        # Extract solution components
        uvwp_cell_new = uvwp_new
        uvw_cell_hat = uvw_hat
        uvw_cell_old = uvw_old

        grad_phi_cell_new_hat = grad_phi
        grad_uvwp_cell_new = grad_phi[:, 0:4]
        grad_uvw_cell_hat = grad_phi[:, 4:7]
        
        # Interpolate cell values to face centers using central difference + skewness correction
        phi_face_new_hat = torch.full(
            (self.cpd_neighbor_cell.shape[1], uvwp_collection.shape[1]), 
            0., device=grad_phi.device
        )
        phi_face_new_hat[self.mask_interior_face] = self.interpolating_phic_to_faces(
            phi_cell=uvwp_collection[:,0:7], # [Cell，Nc] Cell是单元数, Nc是通道数
            grad_phi_cell=grad_phi_cell_new_hat[:,0:7], # [Cell，Nc，3] Cell是单元数, Nc是通道数, 3为3维情况下梯度分量
        )
        phi_face_new_hat[self.mask_boundary_face] = uvwp_collection[self.mask_boundary_cell]

        # Interpolate gradients to face centers
        grad_phi_face_new_hat = torch.full(
            (self.cpd_neighbor_cell.shape[1], grad_phi.shape[1], 3), 
            0., device=grad_phi.device
        )
        grad_phi_face_new_hat[self.mask_interior_face] = self.interpolating_gradients_to_faces(
            phi_cell=uvwp_collection[:,0:7], # [Cell，Nc] Cell是单元数, Nc是通道数
            grad_phi_cell=grad_phi_cell_new_hat[:,0:7], # [Cell，Nc，3] Cell是单元数, Nc是通道数, 3为3维情况下梯度分量
        )
        grad_phi_face_new_hat[self.mask_boundary_face] = grad_phi_cell_new_hat[self.mask_boundary_cell]

        # Extract face-interpolated quantities
        grad_uvwp_face_new = grad_phi_face_new_hat[:, 0:4]
        grad_uvw_face_hat = grad_phi_face_new_hat[:, 4:7]
        
        uvw_face_new = phi_face_new_hat[:, 0:3]
        uvw_face_hat = phi_face_new_hat[:, 4:7]
        p_face_new = phi_face_new_hat[:, 3:4]
        """<<< Interpolation <<<"""

        """>>> Pressure outlet boundary condition >>>"""
        cells_face_outflow_mask = (
            self.face_type[self.cells_face] == NodeType.OUTFLOW
        ).squeeze() 
        
        if cells_face_outflow_mask.any():
            # Compute viscous force at pressure outlet
            viscosity_force_pressure_outlet = (
                self.diffusion_coefficent[self.cells_face_ptr]
                * torch.matmul(
                    grad_uvwp_face_new[self.cells_face, 0:3],
                    self.cells_face_surface_vec.unsqueeze(2),
                ).squeeze()
            )
            # Compute pressure force at outlet
            surface_p = p_face_new[self.cells_face, :] * self.cells_face_surface_vec
            
            # Pressure outlet BC: viscous force should balance pressure force
            loss_press = (viscosity_force_pressure_outlet - surface_p)[cells_face_outflow_mask]
            loss_press = torch.sqrt(
                global_add_pool(
                    (loss_press)**2, 
                    batch=self.batch_face[self.cells_face[cells_face_outflow_mask]],
                    size=self.batch_face.max(dim=0).values + 1
                ).sum(dim=-1, keepdim=True)
            )
        else:
            loss_press = torch.zeros((self.num_graphs, 1), device=p_face_new.device)
        """<<< Pressure outlet boundary condition <<<"""

        # Compute flux contributions
        loss_cont = self.continuity_flux(uvw_face_new=uvw_face_new)  
        convection_flux = self.convective_flux(uvw_face_hat=uvw_face_hat)
        P_flux = self.pressure_flux(p_face_new=p_face_new)
        vis_flux = self.diffusion_flux(grad_uvw_face_hat=grad_uvw_face_hat)

        """>>> Total momentum flux assembly >>>"""
        # Combine all flux contributions: convection + pressure - viscous
        J_flux = torch.matmul(
            convection_flux + P_flux - vis_flux, 
            self.cells_face_surface_vec.unsqueeze(-1)
        ).squeeze()
        
        # Integrate fluxes over cell faces using scatter operation
        total_RHS = scatter_add(
            J_flux,
            self.cells_face_ptr,
            dim=0,
            dim_size=self.cells_volume.shape[0],
        )
        """<<< Total momentum flux assembly <<<"""
        
        # Assemble momentum equation: unsteady + flux_divergence - source = 0
        loss_momentum = (
            unsteady_cell
            + total_RHS
            - self.source_term
        )
        
        # Compute RMS momentum residual
        loss_momentum = torch.sqrt(
            global_add_pool(
                (loss_momentum)**2, batch=self.batch_cell
            )
        )

        return (
            loss_cont,
            loss_momentum,
            loss_press,
            uvwp_cell_new,
        )

    def non_conserved_form(
        self,
        unsteady_cell=None,
        uvwp_new=None,
        uvw_hat=None,
        uvw_old=None,
        uvwp_collection=None,
        grad_phi=None,
    ):
        """
        Compute the non-conserved form of the finite volume equations.
        Args:
            uvwp_new (Tensor): [N_nodes, C] New node values.
            uvw_hat (Tensor): [N_nodes, C] Intermediate node values.
            uvw_old (Tensor): [N_nodes, C] Previous node values.
            uvwp_collection (Tensor): [N_nodes, C] Collection of node variables.
            grad_phi (Tensor): [N_nodes, C, 2] Node gradients.
            graph_cell_x, graph_face, graph_cell, graph_Index: Graph data objects.
        Returns:
            Tuple of loss terms and interpolated node/cell values.
        """

        """>>> Interpolation >>>"""
        # Extract solution components
        uvwp_cell_new = uvwp_new
        uvw_cell_hat = uvw_hat
        uvw_cell_old = uvw_old

        grad_phi_cell_new_hat = grad_phi
        grad_uvwp_cell_new = grad_phi[:, 0:4]
        grad_uvw_cell_hat = grad_phi[:, 4:7]
        
        # Interpolate cell values to face centers using central difference + skewness correction
        phi_face_new_hat = torch.full(
            (self.cpd_neighbor_cell.shape[1], uvwp_collection.shape[1]), 
            0., device=grad_phi.device
        )
        phi_face_new_hat[self.mask_interior_face] = self.interpolating_phic_to_faces(
            phi_cell=uvwp_collection[:,0:7], # [Cell，Nc] Cell是单元数, Nc是通道数
            grad_phi_cell=grad_phi_cell_new_hat[:,0:7], # [Cell，Nc，3] Cell是单元数, Nc是通道数, 3为3维情况下梯度分量
        )
        phi_face_new_hat[self.mask_boundary_face] = uvwp_collection[self.mask_boundary_cell]

        # Interpolate gradients to face centers
        grad_phi_face_new_hat = torch.full(
            (self.cpd_neighbor_cell.shape[1], grad_phi.shape[1], 3), 
            0., device=grad_phi.device
        )
        grad_phi_face_new_hat[self.mask_interior_face] = self.interpolating_gradients_to_faces(
            phi_cell=uvwp_collection[:,0:7], # [Cell，Nc] Cell是单元数, Nc是通道数
            grad_phi_cell=grad_phi_cell_new_hat[:,0:7], # [Cell，Nc，3] Cell是单元数, Nc是通道数, 3为3维情况下梯度分量
        )
        grad_phi_face_new_hat[self.mask_boundary_face] = grad_phi_cell_new_hat[self.mask_boundary_cell]

        # Extract face-interpolated quantities
        grad_uvwp_face_new = grad_phi_face_new_hat[:, 0:4]
        grad_uvw_face_hat = grad_phi_face_new_hat[:, 4:7]
        
        uvw_face_new = phi_face_new_hat[:, 0:3]
        uvw_face_hat = phi_face_new_hat[:, 4:7]
        p_face_new = phi_face_new_hat[:, 3:4]
        """<<< Interpolation <<<"""

        """>>> Pressure outlet boundary condition >>>"""
        cells_face_outflow_mask = (
            self.face_type[self.cells_face] == NodeType.OUTFLOW
        ).squeeze() 
        
        if cells_face_outflow_mask.any():
            # Compute viscous force at pressure outlet
            viscosity_force_pressure_outlet = (
                self.diffusion_coefficent[self.cells_face_ptr]
                * torch.matmul(
                    grad_uvwp_face_new[self.cells_face, 0:3],
                    self.cells_face_surface_vec.unsqueeze(2),
                ).squeeze()
            )
            # Compute pressure force at outlet
            surface_p = p_face_new[self.cells_face, :] * self.cells_face_surface_vec
            
            # Pressure outlet BC: viscous force should balance pressure force
            loss_press = (viscosity_force_pressure_outlet - surface_p)[cells_face_outflow_mask]
            loss_press = torch.sqrt(
                global_add_pool(
                    (loss_press)**2, 
                    batch=self.batch_face[self.cells_face[cells_face_outflow_mask]],
                    size=self.batch_face.max(dim=0).values + 1
                ).sum(dim=-1, keepdim=True)
            )
        else:
            loss_press = torch.zeros((self.num_graphs, 1), device=p_face_new.device)
        """<<< Pressure outlet boundary condition <<<"""

        """>>> Grad-based continuity equation >>>"""
        loss_cont = (
            (grad_uvwp_cell_new[self.mask_interior_cell, 0, 0] + grad_uvwp_cell_new[self.mask_interior_cell, 1, 1]).view(-1,1)
            * self.cells_volume
        )
        loss_cont = torch.sqrt(
            global_add_pool(
                (loss_cont)**2, batch=self.batch_cell
            )
        )*self.continuity_eq_coefficent
        """<<< Grad-based continuity equation <<<"""

        """>>> Grad-based convection term >>>"""
        convection_cell = (
            torch.matmul(grad_uvw_cell_hat[self.mask_interior_cell], uvw_cell_hat[self.mask_interior_cell].unsqueeze(2)).squeeze()
            * self.cells_volume
        )
        """<<< Grad-based convection term  <<<"""

        """>>> grad p term >>>"""
        volume_integrate_P = grad_uvwp_cell_new[self.mask_interior_cell, 2] * self.cells_volume
        """<<< grad p term  <<<"""

        """>>> Divergence-based diffusion term >>>"""
        viscosity_force_cells_face = torch.matmul(
            grad_uvw_face_hat[self.cells_face, 0:3],
            self.cells_face_surface_vec.unsqueeze(2),
        ).squeeze()

        viscosity_force = scatter_add(
                viscosity_force_cells_face,
                self.cells_face_ptr,
                dim=0,
                dim_size=self.cells_volume.shape[0],
            )
        """<<< diffusion term  <<<"""

        loss_momentum = (
            unsteady_cell
            + self.convection_coefficent * convection_cell
            + self.grad_p_coefficent * volume_integrate_P
            - self.diffusion_coefficent * viscosity_force
            - self.source_term
        )

        loss_momentum = torch.sqrt(
            global_add_pool(
                (loss_momentum)**2, batch=self.batch_cell
            )
        )
            
        return (
            loss_cont,
            loss_momentum,
            loss_press,
            uvwp_cell_new,
        )

    # @torch.compile
    def forward(
        self,
        uvwp_cell_new=None,
        uvw_cell_old=None,
        graph_face=None,
        graph_cell=None,
        graph_cell_x=None,
        graph_Index=None,
        params=None,
    ):
        """
        Forward pass for the integrator. Reconstructs gradients and computes loss terms.
        Args:
            uvwp_cell_new (Tensor): [N_cells, C] New cell values.
            uvw_hat_cell (Tensor): [N_cells, C] Intermediate cell values.
            uvw_cell_old (Tensor): [N_cells, C] Previous cell values.
            graph_cell_x, graph_face, graph_cell, graph_Index: Graph data objects.
        Returns:
            Tuple of loss terms and interpolated cell/cell values.
        """
        
        """>>> register geometric quantities for the following interpolation >>>"""
        self.register_geometrics(
            graph_cell, graph_face, graph_Index
        )
        """<<< register geometric quantities for the following interpolation <<<"""
        
        uvwp_cell_new = self.enforce_boundary_condition(uvwp_cell_new, graph_cell)

        """>>> unsteady term >>>"""
        unsteady_cell, uvw_hat_cell = self.time_scheme(
            uvw_cell_old=uvw_cell_old,uvwp_cell_new=uvwp_cell_new
        )
        """<<< unsteady term <<<"""
        
        """>>> Gradient Reconstruction >>>"""
        uvwp_new_uvw_hat = torch.cat(
            (uvwp_cell_new[:, 0:4], uvw_hat_cell[:, 0:3]),
            dim=-1,
        )
        grad_phi = gradient_reconstruction(
            order=params.order,
            phi_node=uvwp_new_uvw_hat,
            edge_index=graph_cell_x.neighbor_cell_x,
            mesh_pos=graph_cell.cpd_centroid,
            precompute_Moments=[graph_cell_x.A_cell_to_cell, graph_cell_x.single_B_cell_to_cell],
        )[:, :, 0:3]  # Extract gradients only: [N_cells, 7_vars, 3_spatial_dims]
        """<<< Gradient Reconstruction <<<"""

        if params.conserved_form:

            (
                loss_cont,
                loss_momentum,
                loss_press,
                uvwp_cell_new,
            ) = self.conserved_form(
                unsteady_cell=unsteady_cell,
                uvwp_new=uvwp_cell_new,
                uvw_hat=uvw_hat_cell,
                uvw_old=uvw_cell_old,
                uvwp_collection=uvwp_new_uvw_hat,
                grad_phi=grad_phi,
            )
        else:
            (
                loss_cont,
                loss_momentum,
                loss_press,
                uvwp_cell_new,
            ) = self.non_conserved_form(
                unsteady_cell=unsteady_cell,
                uvwp_new=uvwp_cell_new,
                uvw_hat=uvw_hat_cell,
                uvw_old=uvw_cell_old,
                uvwp_collection=uvwp_new_uvw_hat,
                grad_phi=grad_phi,
            )

        return (
            loss_cont,
            loss_momentum,
            loss_press,
            uvwp_cell_new,
        )
