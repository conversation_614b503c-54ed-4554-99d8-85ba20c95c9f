import sys
import os

cur_path = os.path.split(__file__)[0]
sys.path.append(cur_path)

import torch
import numpy as np

# import os
from FVdomain import Graph_loader
from Utils import get_param
import time
from Utils.get_param import get_hyperparam
from Utils.Logger import Logger
from torch_geometric.data.batch import Batch
from Utils.utilities import Scalar_Eular_solution
import random
import datetime
from FVmodel.FVdiscretization.FVgrad import weighted_lstsq,compute_normal_matrix,Moving_LSQ,weighted_lstsq_2nd_order

# configurate parameters
params = get_param.params()
seed = int(datetime.datetime.now().timestamp())
np.random.seed(seed)
random.seed(seed)
torch.manual_seed(seed)
torch.cuda.set_per_process_memory_fraction(0.8, params.on_gpu)
torch.set_float32_matmul_precision('high')

device = "cuda"
params.dataset_dir="datasets/Grad_test/lid_driven_cavity_101x101"
params.dataset_size=1
params.batch_size=1
params.order = "2nd" # 1st, 2nd, 3rd, 4th

# 编译 weighted_lstsq 函数 - 只编译主要测试函数
compiled_weighted_lstsq = torch.compile(weighted_lstsq)

# initialize Logger and load model / optimizer if according parameters were given
logger = Logger(
    get_hyperparam(params),
    use_csv=True,
    use_tensorboard=False,
    params=params,
    copy_code=True,
    seed=seed,
)

# initialize Training Dataset
start = time.time()
datasets_factory = Graph_loader.DatasetFactory(
    params=params,
    dataset_dir=params.dataset_dir,
    state_save_dir=logger.saving_path,
    device=device,
)

# refresh dataset size
params.dataset_size = datasets_factory.dataset_size

# create dataset objetc
datasets, loader = datasets_factory.create_loader(
    batch_size=params.batch_size, num_workers=0, pin_memory=False, shuffle=False
)

end = time.time()
print("Training traj has been loaded time consuming:{0}".format(end - start))

''' >>> fetch data and move to GPU >>> '''
for batch_index, fv_graph in enumerate(loader):
    
    case_name = "".join(
        chr(code) for code in fv_graph.graph_node.case_name.tolist()
    )
    
    # Move to device and preprocess
    fv_graph = fv_graph.to(device, exclude_keys=['global_idx'])
    fv_graph = datasets.datapreprocessing_fvgraph(fv_graph)
    ''' <<< fetch data and move to GPU <<< '''
        
    # calcualate phi node value
    phi_node_GT, nabla_phi_GT, hessian_phi_GT = Scalar_Eular_solution(
        mesh_pos=fv_graph.graph_node.pos,
        phi_0=1.0,
        phi_x=0.01,
        phi_y=0.01,
        phi_xy=0.01,
        alpha_x=5,
        alpha_y=5,
        alpha_xy=5,
        L=1.0,
        device=device,
    )

    with torch.no_grad():
        ''' >>> 预计算moments >>> '''
        (A_cell_to_cell, two_way_B_cell_to_cell) = compute_normal_matrix(
            order=params.order,
            mesh_pos=fv_graph.graph_node.pos,
            edge_index=fv_graph.graph_cell_x.neighbor_cell_x,
        )
        single_way_B = torch.chunk(two_way_B_cell_to_cell, 2, dim=0)[0]
        
        # 先运行一次编译版本进行预热，并检查输出
        grad_phi_warmup = compiled_weighted_lstsq(
            phi_node=phi_node_GT,
            edge_index=fv_graph.graph_cell_x.neighbor_cell_x,
            mesh_pos=fv_graph.graph_node.pos,
            order=params.order,
            precompute_Moments=[A_cell_to_cell, single_way_B],
            rt_cond=False,
        )
        print(f"Warmup gradient shape: {grad_phi_warmup.shape}")
        print(f"Warmup gradient contains NaN: {torch.isnan(grad_phi_warmup).any()}")
        ''' <<< 预计算moments <<< '''

        ''' >>> Perform gradient reconstruction 50000 times and calculate average time >>> '''
        total_time = 0.0
        num_runs = 50000
        for _ in range(num_runs):
            start_time = time.time()
            grad_phi = compiled_weighted_lstsq(
                phi_node=phi_node_GT,
                edge_index=fv_graph.graph_cell_x.neighbor_cell_x,
                mesh_pos=fv_graph.graph_node.pos,
                order=params.order,
                precompute_Moments=[A_cell_to_cell, single_way_B],
                rt_cond=False,
            )  
            total_time += time.time() - start_time

        average_time = total_time / num_runs
        print(f"{case_name} Average Grad Rec. Time over {num_runs} runs: {average_time}")
        ''' <<< Perform gradient reconstruction 100 times and calculate average time <<< '''
    
# Calculate the relative L2 error
grad_relative_l2_error_1st = torch.norm(
    grad_phi[:, 0, 0:2] - nabla_phi_GT[:, 0:2], dim=0
) / torch.norm(nabla_phi_GT[:, 0:2], dim=0)
MSE = torch.mean((grad_phi[:, 0, 0:2] - nabla_phi_GT[:, 0:2]) ** 2)
print(f"Gradient Relative L2 error: {grad_relative_l2_error_1st}")
print(f"Gradient MSE: {MSE}")
