"""Utility functions for reading the datasets."""

import sys
import os
file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)

from FVdomain.Graph_dataset import (
    GraphNodeDataset,
    GraphCell_X_Dataset,
    GraphFaceDataset,
    GraphCellDataset,
    Graph_INDEX_Dataset,
)
import torch
import numpy as np
from torch.utils.data import DataLoader as torch_DataLoader
from torch_scatter import scatter_add, scatter_mean
from Post_process.to_vtk import export_full_mesh_vtu,write_vtu_file_2D_poly_to_tri,to_pv_cells_nodes_and_cell_types
from FVdomain.Load_mesh import H5CFDdataset, CFDdatasetBase
from Utils.utilities import NodeType

class Data_Pool:
    def __init__(self, params=None,device=None,state_save_dir=None,):
        """
        Initializes the Data_Pool.

        Args:
            params: Parameters for the dataset.
            device: The device to use (e.g., 'cpu', 'cuda').
            state_save_dir: Directory to save training states.
        """
        self.params = params
        self.device = device
        
        try:
            if not (state_save_dir.find("traing_results") != -1):
                os.makedirs(f"{state_save_dir}/traing_results", exist_ok=True)
                self.state_save_dir = f"{state_save_dir}/traing_results"
        except:
            print(
                ">>>>>>>>>>>>>>>>>>>>>>>Warning, no state_save_dir is specified, check if traing states is specified<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
            )
        
        # Plot the current state of the case being reset
        self._plot_env=True
        
    def _set_reset_env_flag(self, flag=False, rst_time=1):
        """
        Sets the flag for resetting the environment.

        Args:
            flag (bool): The flag to indicate whether to reset the environment.
            rst_time (int): The number of times to reset.
        """
        self.reset_env_flag = flag
        self.rst_time = rst_time

    def load_mesh_to_cpu(
        self,
        dataset_dir=None,
    ):
        """
        Loads the mesh dataset to CPU.

        Args:
            dataset_dir (str): The directory of the dataset.

        Returns:
            tuple: A tuple containing the dataset size and parameters.
        """
        
        valid_h5file_paths = []
        for subdir, _, files in os.walk(dataset_dir):
            for data_name in files:
                if data_name.endswith(".h5"):
                    valid_h5file_paths.append(os.path.join(subdir, data_name))
                    
        if not valid_h5file_paths:
            raise ValueError(".h5 file not FOUND in the specific dataset file dir, plz run src/Parse_mesh/parse_openfoam.py first to generate the .h5 file")
                
        mesh_dataset = H5CFDdataset(
            params=self.params, file_list=valid_h5file_paths
        )

        mesh_loader = torch_DataLoader(
            mesh_dataset,
            batch_size=4,
            num_workers=2,
            pin_memory=False,
            collate_fn=lambda x: x,
        )

        print("loading whole dataset to cpu")
        self.meta_pool = []
        self.uvwp_cell_pool = []
        start_idx = 0
        while True:
            for _, trajs in enumerate(mesh_loader):
                tmp = list(trajs)
                for meta_data, init_uvwp_cell in tmp: # init_uvwp_cell: [num_nodes_in_sample, C]
                    meta_data["global_idx"] = torch.arange(start_idx,start_idx+init_uvwp_cell.shape[0])
                    self.meta_pool.append(meta_data)
                    self.uvwp_cell_pool.append(init_uvwp_cell)
                    start_idx += init_uvwp_cell.shape[0]
 
                    if len(self.meta_pool)>=self.params.dataset_size:
                        break
                    
            if len(self.meta_pool)>=self.params.dataset_size:
                break
            
        print("Successfully load whole dataset to cpu")
        
        self.uvwp_cell_pool = torch.cat(self.uvwp_cell_pool, dim=0) # [total_num_nodes, C]
        self.dataset_size = len(self.meta_pool)
        self.params.dataset_size = self.dataset_size
        
        # --- DEBUGGING START ---
        print("--- Running Debug Check on meta_pool ---")
        if self.meta_pool:
            # Take the first sample as reference
            ref_sample = self.meta_pool[0]
            ref_shapes = {key: value.shape for key, value in ref_sample.items() if torch.is_tensor(value)}

            for i, sample in enumerate(self.meta_pool[1:]):
                for key, value in sample.items():
                    if torch.is_tensor(value):
                        ref_shape = ref_shapes.get(key)
                        if ref_shape is None:
                            print(f"DEBUG WARNING: Key '{key}' in sample {i+1} not in reference sample 0.")
                            continue
                        
                        # Check number of dimensions
                        if value.ndim != len(ref_shape):
                            print(f"--- DEBUG ALERT: Dimension Mismatch Found! ---")
                            print(f"  Sample Index: {i+1}")
                            print(f"  Key: '{key}'")
                            print(f"  Reference Shape (from sample 0): {ref_shape} (ndim={len(ref_shape)})")
                            print(f"  Current Shape: {value.shape} (ndim={value.ndim})")
                            print(f"  Case name: {sample.get('case_name')}, File name: {sample.get('file_name')}")
                            print(f"---------------------------------------------")

        print("--- Debug Check Finished ---")
        # --- DEBUGGING END ---
        
        # Control the folder grouping for the number of plots
        self.plot_count = 0
        return self.dataset_size, self.params
    
    @staticmethod
    def datapreprocessing(
        graph_node, graph_face, graph_cell, graph_cell_x, graph_Index
    ):
        """
        Preprocesses the graph data.

        Args:
            graph_node: Graph node data. graph_node.x: [N, Channel], where N is number of nodes.
            graph_cell_x: Additional graph cell data.
            graph_face: Graph edge data.
            graph_cell: Graph cell data.  [C, Channel], where C is number of cells.
            graph_Index: Graph index data. graph_Index.theta_PDE: [batch_size, C_theta].

        Returns:
            tuple: A tuple containing the preprocessed graph data.
                   graph_cell.x will be [N, 3 + C_theta].
        """

        theta_PDE_cell = graph_Index.theta_PDE[graph_cell.batch] # [N, C_theta] (after broadcasting by batch)
        graph_cell.x = torch.cat((graph_cell.x, theta_PDE_cell), dim=1) # [N, 3 + C_theta]
        
        return (graph_node, graph_face, graph_cell, graph_cell_x, graph_Index)

    @staticmethod
    def datapreprocessing_fvgraph(fv_graph):
        """
        Preprocesses the FVGraph data.

        Args:
            fv_graph (FVGraph): The unified FVGraph object.

        Returns:
            FVGraph: The preprocessed FVGraph object with updated graph_cell.x.
        """
        theta_PDE_cell = fv_graph.graph_index.theta_PDE[fv_graph.graph_cell.batch] # [N, C_theta]
        fv_graph.graph_cell.x = torch.cat((fv_graph.graph_cell.x, theta_PDE_cell), dim=1) # [N, 3 + C_theta]
        
        return fv_graph
    
    def reset_env(self, plot=False):
        """
        Resets the environment by removing the oldest mesh and adding a new one.

        Args:
            plot (bool): Whether to plot the environment state.
        """

        # Pop the mesh data of the 0-th grid
        old_mesh = self.meta_pool.pop(0)
        old_global_idx = old_mesh["global_idx"] # [num_nodes_in_old_mesh]
        
        # Plotting
        if plot:
            uvwp_cell = self.uvwp_cell_pool[old_global_idx] # [num_nodes_in_old_mesh, C]
            mask_interior_cell = old_mesh["cpd|cell_type"].long()==NodeType.NORMAL
            ''' >>> plot at cell-center >>> '''
            self.export_to_tecplot(old_mesh, uvwp_cell[mask_interior_cell], datalocation="cell")
            ''' <<< plot at cell-center <<< '''

            self._plot_env = False

        # Remove uvwp data belonging to the 0-th grid
        self.uvwp_cell_pool = self.uvwp_cell_pool[old_global_idx.shape[0]:] 

        for iidx in range(len(self.meta_pool)):
            cur_meta_data = self.meta_pool[iidx]
            cur_meta_data["global_idx"] -= old_global_idx.shape[0]

        # Then generate new mesh data, i.e., re-select a boundary condition
        new_mesh, init_uvwp = CFDdatasetBase.transform_mesh( # init_uvwp: [num_new_nodes, C]
            old_mesh, 
            self.params
        )
        new_mesh["global_idx"] = torch.arange(
            self.uvwp_cell_pool.shape[0], self.uvwp_cell_pool.shape[0]+init_uvwp.shape[0]
        )
        self.uvwp_cell_pool = torch.cat((self.uvwp_cell_pool, init_uvwp), dim=0)
        self.meta_pool.append(new_mesh)

    def export_to_tecplot(self, mesh, uvwp, datalocation="node", file_name=None):
        """
        Exports data to Tecplot format, supports dynamic variable identification.

        Args:
            mesh (dict): Mesh data.
                         Contains 'node|pos': [N, D_pos], 'case_name', 'cells_node': [num_cells, max_nodes_per_cell],
                         'cells_face': [num_cells, max_faces_per_cell], 'cells_index': [num_cells], 'dt', 'source', 'aoa',
                         'Re' (optional), 'face_node': [num_faces, max_nodes_per_face],
                         'face|neighbor_cell': [num_faces, 2], 'rho', 'mu'.
            uvwp (torch.Tensor): Main physical variable data, typically U, V, P. Shape: [num_elements, C] where num_elements
                                depends on datalocation (N for nodes, num_cells for cells).
            datalocation (str): Data location ("node" or "cell").
            file_name (str, optional): Output file name. Defaults to None.
        """
        
        # Temporarily write vtk for visualization
        mesh_pos = mesh["node|pos"] # [N, D_pos]
        case_name = mesh["case_name"]
        dt = mesh["dt"].squeeze().item()
        source = mesh["source"].squeeze().item()
        aoa = mesh["aoa"].squeeze().item()
        
        try:
            Re=mesh["Re"].squeeze().item()
        except:
            Re=0
            Warning("No Re number in the mesh set to 0")

        if file_name is None:
            save_dir_num = self.plot_count//50
            saving_dir = f"{self.state_save_dir}/{save_dir_num*50}-{(save_dir_num+1)*50}"
            os.makedirs(saving_dir, exist_ok=True)
            saving_path = f"{saving_dir}/NO.{self.plot_count}_{case_name}_Re={Re:.2f}_dt={dt:.3f}_source={source:.2f}_aoa={aoa:.2f}"
        else:
            saving_path = file_name
  
        export_full_mesh_vtu(
            mesh_pos=mesh_pos.cpu(), # [N, D_pos]
            pv_cells_node=mesh["pv_cells_node"], 
            pv_cells_type=mesh["pv_cells_type"],
            data_dict={
                f"{datalocation}|Velocity":uvwp[:,0:3].cpu(), # [num_elements]
                f"{datalocation}|Pressure":uvwp[:,3:4].cpu(), # [num_elements]
            }, 
            save_file_path=f"{saving_path}.vtu",
        )

        self.plot_count+=1

    def update_env(self, mesh):
        """
        Updates the environment, for example, by advancing time steps or changing boundary conditions.

        Args:
            mesh (dict): The mesh data to update.
                         Expected to have 'time_steps', 'flow_type'.
                         If 'wave' in 'flow_type', expects 'mean_u', 'rho', 'mu', 'source', 'aoa', 'dt',
                         'source_frequency', 'source_strength', 'wave_uvwp_on_node'.

        Returns:
            dict: The updated mesh data.
        """
        
        mesh["time_steps"] += 1

        if "wave" in mesh["flow_type"]:
            (
                mesh,
                theta_PDE, # [batch_size_in_cfddatasetbase, C_theta]
                sigma, # [batch_size_in_cfddatasetbase, C_sigma]
                source_pressure_node, # [N, 1]
            ) = CFDdatasetBase.set_Wave_case(
                mesh,
                self.params,
                mesh["mean_u"].item(),
                mesh["rho"].item(),
                mesh["mu"].item(),
                mesh["source"].item(),
                mesh["aoa"].item(),
                mesh["dt"].item(),
                mesh["source_frequency"].item(),
                mesh["source_strength"].item(),
                time_index=mesh["time_steps"],
            )
            mesh["theta_PDE"] = theta_PDE
            mesh["wave_uvwp_on_node"][0, :, 2:3] += source_pressure_node # Assuming wave_uvwp_on_node is [1, N, C_uvwp]

            return mesh

        else: 

            mesh = CFDdatasetBase.To_Cartesian(mesh,resultion=(300,100))

        return mesh

    def refresh_pool(self, uvwp_new, global_idx, graph_index=None):
        """
        Updates the uvwp_cell_pool with new uvwp values and potentially resets the environment.

        Args:
            uvwp_new (torch.Tensor): New uvwP data. Shape: [num_nodes_in_sample, C].
            global_idx (torch.Tensor): Global indices for the uvwP data. Shape: [num_nodes_in_sample].
            graph_index (torch.Tensor, optional): Graph indices for the loss update. Shape: [batch_size_of_graph_index]. Defaults to None.
        """
        
        # update uvwp pool
        self.uvwp_cell_pool[global_idx] = uvwp_new.data

        if self.reset_env_flag:
            for _ in range(self.rst_time):
                
                # Reset the 0-th grid each time, then generate a new grid and append it to the end of the pool
                self.reset_env(plot=self._plot_env)
                
            self.reset_env_flag=False    
            self._plot_env = True
        
        
class DatasetFactory:
    def __init__(
        self,
        params=None,
        dataset_dir=None,
        state_save_dir=None,
        device=None,
    ):
        """
        Factory class to create various graph datasets.

        Args:
            params: Parameters for the dataset.
            dataset_dir (str): Directory of the dataset.
            state_save_dir (str): Directory to save training states.
            device: The device to use (e.g., 'cpu', 'cuda').
        """
        self.base_dataset = Data_Pool(
            params=params,
            device=device,
            state_save_dir=state_save_dir,
        )

        self.dataset_size, self.params = self.base_dataset.load_mesh_to_cpu(
            dataset_dir=dataset_dir,
        )

    def create_loader(self, batch_size=1, num_workers=0, pin_memory=True, shuffle=True):
        """
        Creates and returns a FVGraphLoader that uses a single DataLoader 
        and returns unified FVGraph objects.

        Args:
            batch_size (int): Batch size for the DataLoader.
            num_workers (int): Number of worker processes.
            pin_memory (bool): If True, tensors will be copied to CUDA pinned memory.
            shuffle (bool): Whether to shuffle the dataset.

        Returns:
            tuple: (base_dataset, FVGraphLoader)
        """
        simple_loader = FVGraphLoader(
            base_dataset=self.base_dataset,
            batch_size=batch_size,
            num_workers=num_workers,
            pin_memory=pin_memory,
            shuffle=shuffle
        )

        return self.base_dataset, simple_loader

class FVGraph:
    """
    A unified graph object that contains all graph components.
    Allows access via FVGraph.graph_node, FVGraph.graph_face, etc.
    """
    def __init__(self, graph_node, graph_face, graph_cell, graph_cell_x, graph_index):
        self.graph_node = graph_node
        self.graph_face = graph_face  
        self.graph_cell = graph_cell
        self.graph_cell_x = graph_cell_x
        self.graph_index = graph_index
        
    def to(self, *args, exclude_keys=None, **kwargs):
        """
        Move all graph components to the specified device/dtype.
        
        Args:
            *args: Positional arguments passed to each component's to() method
            exclude_keys: List of keys to exclude from device transfer
            **kwargs: Keyword arguments passed to each component's to() method
        """
        # Modify each component in-place
        self.graph_node = self.graph_node.to(*args, exclude_keys=exclude_keys, **kwargs)
        self.graph_face = self.graph_face.to(*args, exclude_keys=exclude_keys, **kwargs)
        self.graph_cell = self.graph_cell.to(*args, exclude_keys=exclude_keys, **kwargs)
        self.graph_cell_x = self.graph_cell_x.to(*args, exclude_keys=exclude_keys, **kwargs)
        self.graph_index = self.graph_index.to(*args, exclude_keys=exclude_keys, **kwargs)
        return self

    def cuda(self, device=None, exclude_keys=None):
        """
        Move all graph components to CUDA device.
        
        Args:
            device: CUDA device (e.g., 'cuda:0'). If None, uses default CUDA device.
            exclude_keys: List of keys to exclude from device transfer
        """
        return self.to(device or 'cuda', exclude_keys=exclude_keys)


class CombinedGraphDataset:
    """
    A combined dataset that contains all graph component datasets.
    Returns all components for a given index.
    """
    def __init__(self, base_dataset):
        self.base_dataset = base_dataset
        self.graph_node_dataset = GraphNodeDataset(base_dataset=base_dataset)
        self.graph_face_dataset = GraphFaceDataset(base_dataset=base_dataset)
        self.graph_cell_dataset = GraphCellDataset(base_dataset=base_dataset)
        self.graph_cell_x_dataset = GraphCell_X_Dataset(base_dataset=base_dataset)
        self.graph_index_dataset = Graph_INDEX_Dataset(base_dataset=base_dataset)
        
    def __len__(self):
        return len(self.graph_node_dataset)
        
    def __getitem__(self, idx):
        return {
            'graph_node': self.graph_node_dataset[idx],
            'graph_face': self.graph_face_dataset[idx],
            'graph_cell': self.graph_cell_dataset[idx],
            'graph_cell_x': self.graph_cell_x_dataset[idx],
            'graph_index': self.graph_index_dataset[idx]
        }


def collate_fn(batch):
    """
    Custom collate function that batches each graph component separately
    and returns a unified FVGraph object.
    """
    from torch_geometric.data import Batch
    
    # Extract each component from the batch
    graph_nodes = [item['graph_node'] for item in batch]
    graph_faces = [item['graph_face'] for item in batch]
    graph_cells = [item['graph_cell'] for item in batch]
    graph_cell_xs = [item['graph_cell_x'] for item in batch]
    graph_indices = [item['graph_index'] for item in batch]
    
    # Batch each component using PyG's Batch.from_data_list
    batched_graph_node = Batch.from_data_list(graph_nodes)
    batched_graph_face = Batch.from_data_list(graph_faces)
    batched_graph_cell = Batch.from_data_list(graph_cells)
    batched_graph_cell_x = Batch.from_data_list(graph_cell_xs)
    batched_graph_index = Batch.from_data_list(graph_indices)
    
    # 更新self._num_nodes, self._num_faces, self._num_cells, self._num_cpd_cells
    _num_nodes = 0
    for g_node in graph_nodes:
        _num_nodes += g_node._num_nodes
    batched_graph_node._num_nodes = _num_nodes
    
    _num_faces = 0
    for g_face in graph_faces:
        _num_faces += g_face._num_faces
    batched_graph_face._num_faces = _num_faces
    
    _num_cells, _num_cpd_cells = 0, 0
    for g_cell in graph_cells:
        _num_cells += g_cell._num_cells
        _num_cpd_cells += g_cell._num_cpd_cells
    batched_graph_cell._num_cells = _num_cells
    batched_graph_cell._num_cpd_cells = _num_cpd_cells
    
    # Return unified FVGraph object
    return FVGraph(
        graph_node=batched_graph_node,
        graph_face=batched_graph_face,
        graph_cell=batched_graph_cell,
        graph_cell_x=batched_graph_cell_x,
        graph_index=batched_graph_index
    )


class FVGraphLoader:
    """
    Simplified graph loader that uses a single DataLoader and returns FVGraph objects.
    Replaces the complex BatchGraphLoader with a cleaner interface.
    """
    def __init__(self, base_dataset, batch_size=1, num_workers=0, pin_memory=True, shuffle=True):
        self.base_dataset = base_dataset
        self.combined_dataset = CombinedGraphDataset(base_dataset)
        
        self.loader = torch_DataLoader(
            self.combined_dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=pin_memory,
            collate_fn=collate_fn
        )
        
    def __iter__(self):
        """Returns an iterator over FVGraph objects."""
        return iter(self.loader)
        
    def __len__(self):
        return len(self.loader)
        
    def get_specific_data(self, indices):
        """
        Fetches a specific batch of data corresponding to the given indices.
        
        Args:
            indices (list or torch.Tensor): The specific indices to fetch.
            
        Returns:
            tuple: Contains FVGraph object, boolean indicating if boundary conditions 
                   are present, and the original mesh path.
        """
        # Create a subset of the dataset with specific indices
        subset_data = [self.combined_dataset[idx] for idx in indices]
        
        # Use the collate function to create batched data
        fv_graph = collate_fn(subset_data)
        
        # Get metadata from the first index (similar to original implementation)
        minibatch_data = self.base_dataset.meta_pool[indices[0]]

        origin_mesh_path = "".join(
            [chr(int(f)) for f in minibatch_data["origin_mesh_path"][0, :, 0].numpy()]
        )

        flow_type = minibatch_data["flow_type"]
        if ("cavity" in flow_type) or ("possion" in flow_type):
            has_boundary = False
        else:
            has_boundary = True

        return fv_graph, has_boundary, origin_mesh_path


"""
Usage Example:

# Create dataset factory
factory = DatasetFactory(
    params=params,
    dataset_dir="path/to/dataset",
    state_save_dir="path/to/save/states",
    device=device
)

# Option 1: Use the new simplified interface (recommended)
base_dataset, simple_loader = factory.create_loader(
    batch_size=4,
    num_workers=2,
    pin_memory=True,
    shuffle=True
)

# Training loop with simplified interface
for fv_graph in simple_loader:
    # Preprocess the unified graph object
    fv_graph = Data_Pool.datapreprocessing_fvgraph(fv_graph)
    
    # Move to device, excluding global_idx from device transfer
    fv_graph = fv_graph.to(device, exclude_keys=['global_idx'])
    
    # Access individual graph components
    node_features = fv_graph.graph_node.x
    face_features = fv_graph.graph_face.x
    cell_features = fv_graph.graph_cell.x
    # global_idx remains on CPU for memory efficiency
    global_idx = fv_graph.graph_cell.global_idx  # This stays on CPU
    # ... etc
    
    # Your training logic here
    # ...

# Option 2: Get specific data by indices
indices = [0, 1, 2, 3]  # Example indices
fv_graph, has_boundary, origin_mesh_path = simple_loader.get_specific_data(indices)

# Advanced usage examples:

# Example 1: Multiple keys exclusion
fv_graph = fv_graph.to(device, exclude_keys=['global_idx', 'case_name'])

# Example 2: Using cuda method with exclusion
fv_graph = fv_graph.cuda(exclude_keys=['global_idx'])

# Example 3: Moving to specific device with dtype conversion and exclusion
fv_graph = fv_graph.to('cuda:0', dtype=torch.float32, exclude_keys=['global_idx'])

# Example 4: Accessing excluded keys (they remain on CPU)
if hasattr(fv_graph.graph_cell, 'global_idx'):
    global_idx_cpu = fv_graph.graph_cell.global_idx  # This stays on CPU
    print(f"global_idx device: {global_idx_cpu.device}")  # Should print 'cpu'

# Option 3: Use the original complex interface (legacy)
base_dataset, custom_loader, shared_sampler = factory.create_datasets(
    batch_size=4,
    num_workers=2,
    pin_memory=True
)

# Original complex training loop
for graph_node, graph_face, graph_cell, graph_cell_x, graph_index in custom_loader:
    # Original preprocessing
    graph_node, graph_face, graph_cell, graph_cell_x, graph_index = Data_Pool.datapreprocessing(
        graph_node, graph_face, graph_cell, graph_cell_x, graph_index
    )
    # ... rest of the training logic
"""
