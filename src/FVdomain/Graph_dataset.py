"""Utility functions for reading the datasets."""

import sys
import os
file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)

import torch
from torch_geometric.data import InMemoryDataset
from torch_geometric.data import Data


class GraphBase(Data):
    
    # skip copying to cuda device for saving memory
    _skip_to_keys = {''}
    
    def __init__(self, **kwargs):
        """
        Custom graph data structure inheriting from torch_geometric.data.Data.
        """
        super().__init__(**kwargs)
        
        ''' for batching offset '''
        # self._num_nodes = None
        # self._num_faces = None
        # self._num_cells = None
        # self._num_cpd_cells = None
        
    def to(self, *args, exclude_keys=None, **kwargs):
        """
        Override Data.to(): 
        Move tensors to target device/dtype, excluding specified keys.
        
        Args:
            *args: Positional arguments passed to tensor.to()
            exclude_keys: List of keys to exclude from device transfer
            **kwargs: Keyword arguments passed to tensor.to()
        """
        if exclude_keys is None:
            exclude_keys = []
        elif isinstance(exclude_keys, str):
            exclude_keys = [exclude_keys]
        
        # Combine with default skip keys
        all_exclude_keys = set(exclude_keys) | self._skip_to_keys
        
        # Modify in-place to avoid memory duplication
        for key, item in self:
            if isinstance(item, torch.Tensor) and key not in all_exclude_keys:
                self[key] = item.to(*args, **kwargs)
                
        return self

    def cuda(self, device=None, exclude_keys=None):
        """Move to CUDA device with optional key exclusion."""
        return self.to(device or 'cuda', exclude_keys=exclude_keys)
    

    def __inc__(self, key, value, *args, **kwargs):
        """
        Specifies how to increment attributes when concatenating graphs in a batch.
        This is crucial for attributes like edge_index.
        """

        # if not hasattr(self, 'num_nodes') or self.num_nodes is None:
        #     # Ensure num_nodes is available for most offset rules.
        #     # For keys that don't require num_nodes (offset 0), this check might be too strict
        #     # if those keys are present in a graph without num_nodes set.
        #     # However, standard PyG practice is to have num_nodes for batching.
        #     if key not in {"case_name", "query", "grids", "pos", "A_cell_to_cell",
        #                    "A_cell_to_cell_x", "B_cell_to_cell", "B_cell_to_cell_x",
        #                    "single_B_cell_to_cell", "extra_B_cell_to_cell", "cells_volume",
        #                    "node_type", "graph_index", "theta_PDE", "sigma", "uvwp_dim",
        #                    "dt_graph", "x", "y", "m_ids", "m_gs", "global_idx"}:
        #          raise ValueError("The number of nodes must be set before incrementing for key: {}".format(key))

        # Helper function to safely get attributes with default values
        def safe_get_attr(attr_name, default=0):
            return getattr(self, attr_name, default)

        offset_rules = {
            "edge_index": self.num_nodes ,
            "face": self.num_nodes , # keep pyg default value

            "edge_node_x": safe_get_attr('_num_nodes'),

            "cells_node": safe_get_attr('_num_nodes'),
            "cells_node_ptr": safe_get_attr('_num_cells'),

            "face_node": safe_get_attr('_num_nodes'),
            "face_node_ptr": safe_get_attr('_num_faces'),

            "cells_face": safe_get_attr('_num_faces'), # Assuming cells_face refers to face indices, which might need their own offset if not node-based
            "cells_face_ptr": safe_get_attr('_num_cells'), # Assuming cells_face refers to face indices, which might need their own offset if not node-based

            "neighbor_cell": safe_get_attr('_num_cells'), # Assuming neighbor_cell refers to cell indices, which might need their own offset
            "cpd_neighbor_cell": safe_get_attr('_num_cpd_cells'),
            
            "neighbor_cell_x": safe_get_attr('_num_cpd_cells'),

            "cells_index": safe_get_attr('_num_cells'),

            "case_name":0,
            "query": 0,
            "grids": 0,
            "pos": 0,
            "A_cell_to_cell": 0,
            "A_cell_to_cell_x": 0,
            "B_cell_to_cell": 0,
            "B_cell_to_cell_x": 0,
            "single_B_cell_to_cell":0,
            "extra_B_cell_to_cell":0,
            "cells_volume": 0,
            "node_type": 0,
            "graph_index": 0,
            "theta_PDE": 0,
            "sigma": 0,
            "uvwp_dim": 0,
            "dt_graph": 0,
            "x": 0,
            "y": 0,
            "m_ids": 0,
            "m_gs": 0,
            "global_idx": 0,
            "cell_type":0,
            "cpd_cell_type":0,
            "centroid":0,
            "cpd_centroid":0,
            "cell|centroid":0,
            "_num_nodes":0,
            "_num_faces":0,
            "_num_cells":0,
            "_num_cpd_cells":0,
        }
        return offset_rules.get(key, super().__inc__(key, value, *args, **kwargs))

    def __cat_dim__(self, key, value, *args, **kwargs):
        """
        Specifies the dimension along which attributes should be concatenated when creating a batch.
        """
        cat_dim_rules = {
            "x": 0, # Node features: [N, F_node]
            "pos": 0, # Node positions: [N, D_pos]
            "y": 0, # Target values: [N_target, F_target] or [F_target]
            "norm_y": 0,
            "query": 0,  # Keep query as a list, do not concatenate
            "grids": 0,  # Keep query as a list, do not concatenate
            "edge_index": 1,  # Edge indices: [2, num_edges], concatenate along dim 1
            "edge_node_x": 1, # Expanded Edge indices: [2, num_edges], concatenate along dim 1
            "face_node":0,
            "face":0, # Cell-node connectivity or similar, typically [num_cells, nodes_per_cell] or flattened
            "voxel": 0,
            "neighbor_cell":1, # [2, num_neighbor_cell_x_edges]
            "neighbor_cell_x":1, # [2, num_neighbor_cell_x_edges]
            "graph_index": 0, # [batch_size] or [1]
            "global_idx": 0, # [N]
            "cell_type":0,
            "cpd_cell_type":0,
            "centroid":0,
            "cpd_centroid":0,
            "cells_index":0,
            "cell|cells_volume":0,
            "_num_nodes":0,
            "_num_faces":0,
            "_num_cells":0,
            "_num_cpd_cells":0,
        }
        return cat_dim_rules.get(key, super().__cat_dim__(key, value, *args, **kwargs))
    
class GraphNodeDataset(InMemoryDataset):
    def __init__(self, base_dataset):
        """
        Dataset for graph nodes.

        Args:
            base_dataset (Data_Pool): The base Data_Pool instance.
        """
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        """Accesses the meta_pool from the base_dataset."""
        # Here you can filter out GraphNode data from the base class's pool as needed
        return self.base_dataset.meta_pool
    
    def len(self):
        """Returns the number of samples in the dataset."""
        return len(self.pool)

    def get(self, idx):
        """
        Gets a single graph data sample.

        Args:
            idx (int): Index of the sample.

        Returns:
            GraphBase: A graph data object for the specified index.
                - x (torch.Tensor): Node features (uvwp_node). Shape: [N, C_uvwp].
                - edge_index (torch.Tensor): Graph connectivity (face_node). Shape: [2, num_edges].
                - face (torch.Tensor): Cell to node connectivity (cells_node). Shape: [num_cells, max_nodes_per_cell].
                - pos (torch.Tensor): Node positions (mesh_pos). Shape: [N, D_pos].
                - node_type (torch.Tensor): Node types. Shape: [N].
                - y (torch.Tensor): Target node values (target_on_node). Shape: [N, C_target_uvwp].
                - global_idx (torch.Tensor): Global indices of nodes. Shape: [N].
                - case_name (torch.Tensor): Case name encoded as tensor of ordinals. Shape: [L_case_name].
                - graph_index (torch.Tensor): Index of the graph in the batch. Shape: [1].
        """
        minibatch_data = self.pool[idx]

        mesh_pos = minibatch_data["node|pos"].to(torch.float32) # [N, D_pos]
        # node_type = minibatch_data["node|node_type"].long().squeeze() # [N]
        face_node = minibatch_data["face_node"].long() # [2, num_edges] (assuming it's edge_index like) or [num_faces, nodes_per_face]
        cells_node = minibatch_data["cells_node"].long() # [num_cells*max_nodes_per_cell]

        graph_node = GraphBase(
            # node_type=node_type,
            face_node=face_node,
            cells_node=cells_node,
            num_nodes=mesh_pos.shape[0], # number of nodes in the graph
            _num_nodes=mesh_pos.shape[0],
            # pos=mesh_pos,
            graph_index=torch.tensor([idx],dtype=torch.long),
        )

        return graph_node

class GraphFaceDataset(InMemoryDataset):
    def __init__(self, base_dataset):
        """
        Dataset for graph edges.

        Args:
            base_dataset (Data_Pool): The base Data_Pool instance.
        """
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        """Accesses the meta_pool from the base_dataset."""
        # Here you can filter out GraphNode data from the base class's pool as needed
        return self.base_dataset.meta_pool

    def len(self):
        """Returns the number of samples in the dataset."""
        return len(self.pool)

    def get(self, idx):
        """
        Gets a single graph data sample for edge features.

        Args:
            idx (int): Index of the sample.

        Returns:
            GraphBase: A graph data object for edge attributes.
                - face_type (torch.Tensor): Type of each face/edge. Shape: [num_faces].
                - face_area (torch.Tensor): Area of each face/edge. Shape: [num_faces, 1] or [num_faces].
                - face (torch.Tensor): Cell to face connectivity (cells_face). Shape: [num_cells, max_faces_per_cell].
                - pos (torch.Tensor): Positions of face centers (face_center_pos). Shape: [num_faces, D_pos].
                - graph_index (torch.Tensor): Index of the graph in the batch. Shape: [1].
        """
        minibatch_data = self.pool[idx]

        # edge_attr
        face_area = minibatch_data["face|face_area"].to(torch.float32) # [num_faces, 1] or [num_faces]
        face_type = minibatch_data["face|face_type"].long().squeeze() # [num_faces]
        face_center_pos = minibatch_data["face|face_center_pos"].to(torch.float32) # [num_faces, D_pos]
        cells_face = minibatch_data["cells_face"].long() # [num_cells, max_faces_per_cell]
        face_node_ptr = minibatch_data["face_node_ptr"].long() # [num_faces]
        
        graph_face = GraphBase(
            face_type=face_type,
            face_area=face_area,
            cells_face=cells_face,
            face_node_ptr=face_node_ptr,
            pos=face_center_pos,
            _num_faces=face_center_pos.shape[0],
            graph_index=torch.tensor([idx],dtype=torch.long),
        )

        return graph_face

class GraphCellDataset(InMemoryDataset):
    def __init__(self, base_dataset):
        """
        Dataset for graph cells.

        Args:
            base_dataset (Data_Pool): The base Data_Pool instance.
        """
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        """Accesses the meta_pool from the base_dataset."""
        # Here you can filter out GraphNode data from the base class's pool as needed
        return self.base_dataset.meta_pool

    def len(self):
        """Returns the number of samples in the dataset."""
        return len(self.pool)

    def get(self, idx):
        """
        Gets a single graph data sample for cell features.

        Args:
            idx (int): Index of the sample.

        Returns:
            GraphBase: A graph data object for cell attributes.
                - x (torch.Tensor): Placeholder cell features. Shape: [num_cells, 3].
                - edge_index (torch.Tensor): Cell connectivity (neighbor_cell). Shape: [2, num_cell_edges] or [num_faces, 2].
                - cells_face_unv (torch.Tensor): Unit normal vectors for cell faces. Shape: [num_cells, max_faces_per_cell, D_norm] or other.
                - cells_volume (torch.Tensor): Area of each cell. Shape: [num_cells, 1] or [num_cells].
                - pos (torch.Tensor): Cell centroids. Shape: [num_cells, D_pos].
                - face (torch.Tensor): Cell indices (cells_index). Shape: [num_cells].
                - graph_index (torch.Tensor): Index of the graph in the batch. Shape: [1].
        """
        minibatch_data = self.pool[idx]

        case_name = minibatch_data["case_name"]

        # cell_attr
        neighbor_cell = minibatch_data["face|neighbor_cell"].long() # [num_faces, 2] (cell adjacency through faces)
        cpd_neighbor_cell = minibatch_data["cpd|neighbor_cell"].long() # [num_faces, 2] (cell adjacency through faces)
        cells_volume = minibatch_data["cell|cells_volume"].to(torch.float32) # [num_cells, 1] or [num_cells]
        centroid = minibatch_data["cell|centroid"].to(torch.float32) # [num_cells, D_pos]
        cpd_centroid = minibatch_data["cpd|centroid"].to(torch.float32) # [num_cells, D_pos]
        cells_face_unv = minibatch_data['cells_face_normal'].to(torch.float32) # Shape depends on definition, e.g., [num_cells, max_faces_per_cell, D_norm]
        cpd_cell_type = minibatch_data["cpd|cell_type"].long() # [num_cells, 1]
        cells_node_ptr = minibatch_data["cells_node_ptr"].long() # 
        cells_face_ptr = minibatch_data["cells_face_ptr"].long() #    
        
        # velocity field and pressure field
        global_idx = minibatch_data["global_idx"].long() # [N]
        uvwp_cell = self.base_dataset.uvwp_cell_pool[global_idx] # [N, C_uvwp]
        target_on_cell = minibatch_data["target|uvwp"].to(torch.float32) # [N, C_target_uvwp]

        graph_cell = GraphBase(
            x=uvwp_cell, # [num_compond_cells, 4]
            cpd_neighbor_cell=cpd_neighbor_cell,
            neighbor_cell=neighbor_cell,
            cells_face_unv=cells_face_unv,
            cells_volume=cells_volume,

            cpd_cell_type=cpd_cell_type,
            centroid=centroid,
            cpd_centroid=cpd_centroid,
            y=target_on_cell,
            global_idx=global_idx,
            
            _num_cells=cells_volume.shape[0], # number of cells ''' ---> be very Carful here '''
            _num_cpd_cells=cpd_centroid.shape[0], 
            num_nodes=cpd_centroid.shape[0], # number of compond cells ''' ---> be very Carful here '''

            cells_node_ptr=cells_node_ptr,
            cells_face_ptr=cells_face_ptr,
            
            case_name=torch.tensor([ord(char) for char in (case_name)], dtype=torch.long),
            graph_index=torch.tensor([idx],dtype=torch.long),
        )

        return graph_cell
    
class GraphCell_X_Dataset(InMemoryDataset):
    """This graph is undirected. Dataset for auxiliary node features and connectivity."""

    def __init__(self, base_dataset):
        """
        Initializes the dataset for auxiliary node features.

        Args:
            base_dataset (Data_Pool): The base Data_Pool instance.
        """
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        """Accesses the meta_pool from the base_dataset."""
        # Here you can filter out GraphNode data from the base class's pool as needed
        return self.base_dataset.meta_pool

    def len(self):
        """Returns the number of samples in the dataset."""
        return len(self.pool)

    def get(self, idx):
        """
        Gets a single graph data sample with auxiliary node features.

        Args:
            idx (int): Index of the sample.

        Returns:
            GraphBase: A graph data object.
                - neighbor_cell_x (torch.Tensor): Extended face-node connectivity. Shape: [2, num_neighbor_cell_x_edges].
                - num_nodes (int): Number of nodes in the graph.
                - A_cell_to_cell (torch.Tensor): Node-to-node matrix A. Shape: [N, N] or other.
                - single_B_cell_to_cell (torch.Tensor): Node-to-node matrix single_B. Shape: [N, N] or other.
                - extra_B_cell_to_cell (torch.Tensor): Node-to-node matrix extra_B. Shape: [N, N] or other.
                - graph_index (torch.Tensor): Index of the graph in the batch. Shape: [1].
        """

        minibatch_data = self.pool[idx]

        """Optional cell attr"""
        num_cpd_cells = minibatch_data["cpd|centroid"].shape[0] # [N, D_pos]
        neighbor_cell_x = minibatch_data["neighbor_cell_x"].long() # [2, num_neighbor_cell_x_edges]
        A_cell_to_cell = minibatch_data["A_cell_to_cell"].to(torch.float32) # Shape depends on definition
        single_B_cell_to_cell = minibatch_data["single_B_cell_to_cell"].to(torch.float32) # Shape depends on definition

        graph_cell_x = GraphBase(
            neighbor_cell_x=neighbor_cell_x, # only for WLSQ
            _num_cpd_cells=num_cpd_cells, # actually number of compond cells
            num_nodes=num_cpd_cells,
            A_cell_to_cell=A_cell_to_cell,
            single_B_cell_to_cell=single_B_cell_to_cell,
            graph_index=torch.tensor([idx],dtype=torch.long),
        )

        return graph_cell_x

class Graph_INDEX_Dataset(InMemoryDataset):
    def __init__(self, base_dataset):
        """
        Dataset for graph-level index information.

        Args:
            base_dataset (Data_Pool): The base Data_Pool instance.
        """
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        """Accesses the meta_pool from the base_dataset."""
        # Here you can filter out GraphNode data from the base class's pool as needed
        return self.base_dataset.meta_pool
    
    @property
    def params(self):
        """Accesses the params from the base_dataset."""
        return self.base_dataset.params

    def len(self):
        """Returns the number of samples in the dataset."""
        return len(self.pool)

    def get(self, idx):
        """
        Gets a single graph data sample for graph-level indices/parameters.

        Args:
            idx (int): Index of the sample.

        Returns:
            GraphBase: A graph data object.
                - x (torch.Tensor): Index of the graph. Shape: [1].
                - theta_PDE (torch.Tensor): PDE parameters. Shape: [C_theta].
                - uvwp_dim (torch.Tensor): Dimensions of uvwP. Shape: [C_uvwp_dim].
                - dt_graph (torch.Tensor): Timestep for the graph. Shape: [1] or other.
                - graph_index (torch.Tensor): Index of the graph in the batch. Shape: [1].
        """
        minibatch_data = self.pool[idx]
        
        theta_PDE = minibatch_data["theta_PDE"].to(torch.float32) # Shape: [C_theta]
        uvwp_dim = minibatch_data["uvwp_dim"].to(torch.float32) # Shape: [C_uvwp_dim]
        dt_graph = minibatch_data["dt_graph"].to(torch.float32) # Shape: [1] or other
        
        graph_Index = GraphBase(
            x=torch.tensor([idx],dtype=torch.long),
            theta_PDE=theta_PDE,
            uvwp_dim=uvwp_dim,
            dt_graph=dt_graph,
            graph_index=torch.tensor([idx],dtype=torch.long),
        )

        return graph_Index